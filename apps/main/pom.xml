<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns="http://maven.apache.org/POM/4.0.0"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>fr.enedis.i2r</groupId>
        <artifactId>apps</artifactId>
        <version>1.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>main</artifactId>

    <properties>
        <main-class>fr.enedis.i2r.main.Main</main-class>
    </properties>

    <dependencies>
        <dependency>
            <groupId>fr.enedis.i2r</groupId>
            <artifactId>infra</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>fr.enedis.i2r</groupId>
            <artifactId>comsi</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>fr.enedis.i2r</groupId>
            <artifactId>security</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>fr.enedis.i2r</groupId>
            <artifactId>system</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <version>3.3.0</version>
                <configuration>
                    <classpathScope>compile</classpathScope>
                    <mainClass>${main-class}</mainClass>
                    <systemProperties>
                    <systemProperty>
                        <key>logback.configurationFile</key>
                        <value>${project.basedir}/src/main/resources/logback.xml</value>
                    </systemProperty>
                    </systemProperties>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifest>
                            <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                            <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
                            <mainClass>${main-class}</mainClass>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>${assembly.version}</version>
                <configuration>
                    <archive>
                        <manifest>
                            <mainClass>${main-class}</mainClass>
                        </manifest>
                    </archive>
                    <descriptorRefs>
                        <descriptorRef>jar-with-dependencies</descriptorRef>
                    </descriptorRefs>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
