# README

Ce projet est l'implémentation de I2R

## P<PERSON>s requis

Pour exécuter ce projet en local, il est nécessaire :

* d'avoir démarré le projet "hal-mock" qui simule le hardware du BIP
* de fournir à la commande de lancement les dépendances "provided", soit

````shell
java ... -classpath /home/<USER>/.m2/repository/org/xerial/sqlite-jdbc/********/sqlite-jdbc-********.jar
````

sous IntelliJ ceci se fait dans "Edit configurations..." > "Modify options" > "Add dependencies with "provided" scope to classpath"
