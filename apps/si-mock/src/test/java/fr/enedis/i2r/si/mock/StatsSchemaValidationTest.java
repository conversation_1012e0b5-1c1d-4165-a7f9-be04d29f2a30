package fr.enedis.i2r.si.mock;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

import org.junit.jupiter.api.Test;

public class StatsSchemaValidationTest {

    @Test
    public void le_json_d_exemple_stats_icom_est_valide_par_rapport_au_schema() throws IOException {
        String exampleJson = Files.readString(
            Paths.get("src/main/resources/icom-stats-example.json")
        );

        boolean isValid = ConfigurationValidator.validateStatsBoitier(exampleJson);

        assertTrue(isValid, "The ICOM stats example JSON should validate against the schema");
    }

    @Test
    public void un_json_contenant_un_seul_objet_statcellserving_est_valide() {
        String containerWithStatCellServing = """
            {
                "class": "Container",
                "cmdKey": null,
                "emitted": "2025-09-22T13:37:41Z",
                "entity": "LOGDM",
                "flow": "/db/cfg/flows/stat",
                "modified": "2025-09-21T22:00:02Z",
                "name": "test",
                "nb": 1,
                "pub": true,
                "objects": [
                    {
                        "class": "StatCellServing",
                        "cmdKey": null,
                        "emitted": "2025-09-22T13:37:41Z",
                        "entity": "LOGDM",
                        "flow": null,
                        "modified": "2025-09-21T21:59:46Z",
                        "name": "StatCellServing",
                        "pub": true,
                        "dateMes": "2025-09-20T22:00:02Z",
                        "mcc": 208,
                        "mnc": 1,
                        "rsrp": -85,
                        "rsrq": -12,
                        "rssi": -65,
                        "techno": "4G",
                        "earfcn": 1850,
                        "sinr": 15,
                        "pci": 123,
                        "tac": 456
                    }
                ]
            }
            """;

        boolean isValid = ConfigurationValidator.validateStatsBoitier(containerWithStatCellServing);

        assertTrue(isValid, "A JSON with only StatCellServing should validate against the schema");
    }

    @Test
    public void un_json_contenant_statcellserving_avec_champs_optionnels_est_valide() {
        String containerWithOptionalFields = """
            {
                "class": "Container",
                "cmdKey": null,
                "emitted": "2025-09-22T13:37:41Z",
                "entity": "LOGDM",
                "flow": "/db/cfg/flows/stat",
                "modified": "2025-09-21T22:00:02Z",
                "name": "test",
                "nb": 1,
                "pub": true,
                "objects": [
                    {
                        "class": "StatCellServing",
                        "cmdKey": null,
                        "emitted": "2025-09-22T13:37:41Z",
                        "entity": "LOGDM",
                        "flow": null,
                        "modified": "2025-09-21T21:59:46Z",
                        "name": "StatCellServing",
                        "pub": true,
                        "dateMes": "2025-09-20T22:00:02Z",
                        "mcc": 208,
                        "mnc": 1,
                        "rsrp": -85,
                        "rsrq": -12,
                        "rssi": -65,
                        "techno": "4G",
                        "arfcn": 512,
                        "ber": 2,
                        "ci": 12345,
                        "ecno": -8,
                        "lac": 789,
                        "rscp": -75,
                        "rxLevel": -70,
                        "rxQual": 3,
                        "sRxLevel": -68,
                        "ta": 5,
                        "earfcn": 1850,
                        "sinr": 15,
                        "pci": 123,
                        "tac": 456
                    }
                ]
            }
            """;

        boolean isValid = ConfigurationValidator.validateStatsBoitier(containerWithOptionalFields);

        assertTrue(isValid, "A JSON with StatCellServing including optional fields should validate against the schema");
    }

    @Test
    public void un_json_contenant_plusieurs_objets_est_valide() {
        String containerWithStatWAN = """
            {
                "class": "Container",
                "cmdKey": null,
                "emitted": "2025-09-22T13:37:41Z",
                "entity": "LOGDM",
                "flow": "/db/cfg/flows/stat",
                "modified": "2025-09-21T22:00:02Z",
                "name": "test",
                "nb": 1,
                "pub": true,
                "objects": [
                    {
                        "class": "StatWAN",
                        "bpsRxAv": 14,
                        "bpsRxMax": 6891,
                        "bpsRxMin": 0,
                        "bpsTxAv": 20,
                        "bpsTxMax": 11020,
                        "bpsTxMin": 0,
                        "cmdKey": null,
                        "date": "2025-09-20T22:00:02Z",
                        "duration": 86398,
                        "emitted": "2025-09-22T13:37:41Z",
                        "entity": "LOGDM",
                        "flow": null,
                        "httpTmo": 0,
                        "logs": [],
                        "modified": "2025-09-21T21:59:46Z",
                        "name": "StatWAN",
                        "nbOctets": 29394097,
                        "nbOctetsCourant": 9134998,
                        "pdpKO": 1,
                        "pdpOK": 3,
                        "pub": true,
                        "rxHttpAPI": 0,
                        "rxHttpDownload": 0,
                        "rxHttpWeb": 73396,
                        "rxNtp": 400,
                        "rxPing": 24360,
                        "rxSsh": 0,
                        "rxTotal": 123694,
                        "tcpCnxKO": 0,
                        "tcpCnxOK": 0,
                        "tcpDisconnect": 0,
                        "tcpTmo": 0,
                        "txHttpAPI": 0,
                        "txHttpDownload": 0,
                        "txHttpWeb": 153922,
                        "txNtp": 400,
                        "txPing": 24528,
                        "txSsh": 0,
                        "txTotal": 205056
                    },
                    {
                        "class": "StatAlarms",
                        "cmdKey": null,
                        "date": "2025-09-20T22:00:02Z",
                        "duration": 86398,
                        "emitted": "2025-09-22T13:37:41Z",
                        "entity": "LOGDM",
                        "flow": null,
                        "ignored": 0,
                        "lost": 0,
                        "modified": "2025-09-21T21:59:46Z",
                        "name": "StatAlarms",
                        "notSent": 0,
                        "pub": true
                    }
                ]
            }
            """;

        boolean isValid = ConfigurationValidator.validateStatsBoitier(containerWithStatWAN);

        assertTrue(isValid, "A JSON with only StatWAN should validate against the schema");
    }

    @Test
    public void un_json_minimal_est_valide() {
        String minimalJson = """
            {
                "class": "Container",
                "cmdKey": null,
                "emitted": "2025-09-22T13:37:41Z",
                "entity": "LOGDM",
                "flow": "/db/cfg/flows/stat",
                "modified": "2025-09-21T22:00:02Z",
                "name": "test",
                "nb": 0,
                "objects": [],
                "pub": true
            }
            """;

        boolean isValid = ConfigurationValidator.validateStatsBoitier(minimalJson);

        assertTrue(isValid, "A minimal JSON should validate against the schema");
    }

        @Test
    public void un_json_incomplet_n_est_pas_valide() {
        String incompleteJson = """
            {
                "class": "Container",
                "entity": "LOGDM",
                "name": "test",
                "pub": false
            }
            """;

        boolean isValid = ConfigurationValidator.validateStatsBoitier(incompleteJson);

        assertFalse(isValid, "An incomplete JSON should not validate against the schema");
    }

    @Test
    public void un_json_vide_n_est_pas_valide() {
        String emptyJson = "{ }";

        boolean isValid = ConfigurationValidator.validateStatsBoitier(emptyJson);

        assertFalse(isValid, "An empty JSON should not validate against the schema");
    }
}
