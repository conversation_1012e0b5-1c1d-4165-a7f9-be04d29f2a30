package fr.enedis.i2r.si.mock;

import static io.javalin.http.HttpStatus.OK;

import java.io.FileInputStream;
import java.net.http.HttpClient;
import java.security.KeyStore;
import java.security.SecureRandom;

import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManagerFactory;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.infra.rest.si.SiEndpoint;
import io.javalin.Javalin;
import io.javalin.community.ssl.SslPlugin;
import io.javalin.config.JavalinConfig;
import io.javalin.openapi.plugin.OpenApiPlugin;
import io.javalin.openapi.plugin.swagger.SwaggerPlugin;

public class Main {

    private static final Logger logger = LoggerFactory.getLogger(Main.class);

    private static final Integer SECURE_PORT = 8443;
    private static final Integer INSECURE_PORT = 8440;
    public static final String PASSWORD = "password";

    public static void main(String[] args) {
        logger.debug("Starting SI Mock...");
        Javalin app = Javalin.create(Main::configureHttpServer);
        HttpClient httpClient = createHttpClientWithTrustStore();

        var siController = new SiController();
        var bipCaller = new BipCaller(httpClient);
        app.put(SiEndpoint.SendConfig.endpointTemplate, ctx -> {
            siController.receiveConfiguration(ctx);
            if (ctx.status() == OK) {
                bipCaller.setStableStatus(ctx);
            }
        });

        app.get("trigger-cfg-request", ctx -> {
            bipCaller.requestFullCfg(ctx);
        });

        app.start();

        logger.debug("REST Server has started and is running...");
        logger.info("Swagger docs at https://localhost:" + SECURE_PORT + "/swagger");
        logger.info("OpenAPI JSON at https://localhost:" + SECURE_PORT + "/openapi");
        logger.info("Swagger docs at http://localhost:" + INSECURE_PORT + "/swagger");
        logger.info("OpenAPI JSON at http://localhost:" + INSECURE_PORT + "/openapi");

        System.out.println(" ▄▀▀ █    █▄ ▄█ ▄▀▄ ▄▀▀ █▄▀     ▄▀▄ █▄ █");
        System.out.println(" ▄██ █ ▀▀ █ ▀ █ ▀▄▀ ▀▄▄ █ █     ▀▄▀ █ ▀█");
    }

    private static void configureHttpServer(JavalinConfig config) {
        logger.debug("Configuring REST OpenAPI...");
        config.registerPlugin(new OpenApiPlugin(pluginConfig -> {
            pluginConfig.withDefinitionConfiguration((version, definition) -> {
                definition.withInfo(info -> {
                    info.setTitle("OpenAPI SI Mock");
                    info.setVersion("1.0.0");
                    info.setDescription("Documentation de l'API du Mock SI (iCom/iCoeur/iCare)");
                });
            });
        }));
        config.registerPlugin(new SwaggerPlugin());
        config.showJavalinBanner = false;

        SslPlugin sslPlugin = new SslPlugin(conf -> {
            conf.keystoreFromPath("/var/lib/i2r/si-mock-certificate.jks", PASSWORD);
            conf.withTrustConfig(trustConfig -> {
                trustConfig.trustStoreFromPath("/var/lib/i2r/si-mock-truststore.jks", PASSWORD);
            });
            conf.secure = true;
            conf.securePort = SECURE_PORT;
            conf.insecure = true;
            conf.insecurePort = INSECURE_PORT;
            conf.sniHostCheck = false;
            conf.http2 = true;
        });

        config.registerPlugin(sslPlugin);
    }

    private static HttpClient createHttpClientWithTrustStore() {
        try {
            // Load the trust store that contains the BIP's certificate
            KeyStore trustStore = KeyStore.getInstance("JKS");
            try (FileInputStream fis = new FileInputStream("/var/lib/i2r/si-mock-truststore.jks")) {
                trustStore.load(fis, PASSWORD.toCharArray());
            }

            // Create TrustManagerFactory with the trust store
            TrustManagerFactory tmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
            tmf.init(trustStore);

            // Load client certificate keystore for client authentication
            KeyStore clientKeyStore = KeyStore.getInstance("JKS");
            try (FileInputStream fis = new FileInputStream("/var/lib/i2r/si-mock-certificate.jks")) {
                clientKeyStore.load(fis, PASSWORD.toCharArray());
            }
            KeyManagerFactory kmf = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
            kmf.init(clientKeyStore, PASSWORD.toCharArray());

            // Create SSL context with both trust managers and key managers
            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(kmf.getKeyManagers(), tmf.getTrustManagers(), new SecureRandom());

            // Create HttpClient with the SSL context
            return HttpClient.newBuilder()
                    .sslContext(sslContext)
                    .build();
        } catch (Exception e) {
            logger.error("Failed to create HttpClient with SSL configuration, falling back to default", e);
            return HttpClient.newHttpClient();
        }
    }
}
