package fr.enedis.i2r.si.mock;

import static fr.enedis.i2r.comsi.rest.HttpsServer.SECURE_PORT;
import static fr.enedis.i2r.comsi.rest.RestEndpoints.DB_CFG;
import static fr.enedis.i2r.comsi.rest.RestEndpoints.STATUS_CHANGE;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.net.http.HttpResponse.BodyHandlers;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.comsi.IpAddress;
import fr.enedis.i2r.infra.rest.SiHeaders;
import io.javalin.http.Context;
import io.javalin.openapi.HttpMethod;
import io.javalin.openapi.OpenApi;

public class BipCaller {
    private static final Logger logger = LoggerFactory.getLogger(BipCaller.class);

    HttpClient client;

    public BipCaller(HttpClient client) {
        this.client = client;
    }

    public void setStableStatus(Context ctx) throws IOException, InterruptedException {
        String configHash = Optional.ofNullable(ctx.header(SiHeaders.Constants.X_ERDF_HASH_HEADER)).orElseThrow();

        IpAddress bipIp = new IpAddress("127.0.0.1");
        int bipPort = SECURE_PORT;

        logger.info("Send setStableStatus");
        logger.info("Hash : {}", configHash);
        logger.info("");

        String idms = ctx.pathParam("idms");

        String jsonPayload = "{\"name\":\"dm\",\"class\":\"DM\",\"state\":2}";
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create("https://" + bipIp + ":" + bipPort + STATUS_CHANGE))
                .header("Accept", "text/plain, application/json, application/yaml, application/*+json, */*")
                .header("Accept-Encoding", "gzip, x-gzip, deflate")
                .header("Content-Type", "text/plain;charset=ISO-8859-1")
                .header("User-Agent", "Apache-HttpClient/5.4.2 (Java/17.0.3)")
                .header("X-ERDF-API-VERSION", "2.0")
                .header("x-idms", idms)
                .header("X-ERDF-HASH", configHash)
                .PUT(HttpRequest.BodyPublishers.ofString(jsonPayload))
                .build();


        HttpResponse<String> response = client.send(request, BodyHandlers.ofString());

        logger.info("Status : {}", response.statusCode());
        logger.info("Contenu : {}", response.body());
        logger.info("Content-Type : {}", response.headers().firstValue("Content-Type").orElse("inconnu"));
    }

    @OpenApi(
        summary = "Demande la config du boitier",
        operationId = "askConfiguration",
        path = "/trigger-cfg-request",
        methods = HttpMethod.GET,
        tags = { "Back-end" }
    )
    public void requestFullCfg(Context ctx) throws IOException, InterruptedException {

        IpAddress bipIp = new IpAddress("127.0.0.1");
        int bipPort = SECURE_PORT;

        logger.info("Send requestFullCfg");
        logger.info("");

        // IDMS DE TEST
        String idms ="475000DE007860000010000001000223";

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create("https://" + bipIp + ":" + bipPort + DB_CFG))
                .header("Accept", "text/plain, application/json, application/yaml, application/*+json, */*")
                .header("Accept-Encoding", "gzip, x-gzip, deflate")
                .header("X-Erdf-Api-Version", "2.0")
                .header("X-Idms", idms)
                .GET()
                .build();


        HttpResponse<String> response = client.send(request, BodyHandlers.ofString());

        logger.info("Status : {}", response.statusCode());
        logger.info("Contenu : {}", response.body());
        logger.info("Content-Type : {}", response.headers().firstValue("Content-Type").orElse("inconnu"));
    }
}
