{"$schema": "http://json-schema.org/draft-04/schema#", "properties": {"cmdKey": {"type": ["string", "null"]}, "emitted": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "entity": {"type": ["string", "null"]}, "flow": {"type": ["string", "null"]}, "modified": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "pub": {"type": "boolean"}, "objects": {"additionalItems": false, "items": {"anyOf": [{"type": "object", "properties": {"cmdKey": {"type": ["string", "null"]}, "emitted": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "entity": {"type": ["string", "null"]}, "flow": {"type": ["string", "null"]}, "modified": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "pub": {"type": "boolean"}, "lastValidityCheck": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "nextValidityCheck": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "notAfter": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "ttl": {"type": "integer"}, "class": {"type": "string", "enum": ["Certificate<PERSON>heck"]}, "name": {"type": "string", "enum": ["certificate"]}}, "required": ["name", "class"]}, {"type": "object", "properties": {"cmdKey": {"type": ["string", "null"]}, "emitted": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "entity": {"type": ["string", "null"]}, "flow": {"type": ["string", "null"]}, "modified": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "pub": {"type": "boolean"}, "class": {"type": "string", "enum": ["Container"]}, "name": {"type": "string", "enum": ["device"]}, "nb": {"type": "integer", "enum": [7]}, "objects": {"additionalItems": false, "items": {"anyOf": [{"type": "object", "properties": {"cmdKey": {"type": ["string", "null"]}, "emitted": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "entity": {"type": ["string", "null"]}, "flow": {"type": ["string", "null"]}, "modified": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "pub": {"type": "boolean"}, "enable": {"type": "boolean"}, "status": {"type": ["integer", "null"]}, "IPsecEndPoint": {"type": ["array", "null"], "items": {"type": ["string", "null"]}}, "macAddress": {"type": ["string", "null"]}, "class": {"type": "string", "enum": ["Box"]}, "name": {"type": "string", "enum": ["box"]}}, "required": ["name", "class"]}, {"type": "object", "properties": {"cmdKey": {"type": ["string", "null"]}, "emitted": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "entity": {"type": ["string", "null"]}, "flow": {"type": ["string", "null"]}, "modified": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "pub": {"type": "boolean"}, "enable": {"type": "boolean"}, "ipv4Address": {"type": "object", "additionalProperties": false, "properties": {"enable": {"type": "integer"}, "address": {"type": ["string", "null"]}, "subnet": {"type": ["string", "null"]}, "gateway": {"type": ["string", "null"]}, "addressingType": {"type": ["string", "null"]}}}, "ipv6Address": {"type": "object", "additionalProperties": false, "properties": {"enable": {"type": "integer"}, "address": {"type": ["string", "null"]}, "subnet": {"type": ["integer", "null"]}, "gateway": {"type": ["string", "null"]}, "addressingType": {"type": ["string", "null"]}}}, "macAddress": {"type": ["string", "null"]}, "status": {"type": ["integer", "null"]}, "class": {"type": "string", "enum": ["Ethernet"]}, "name": {"type": "string", "enum": ["ethernet"]}}, "required": ["name", "class"]}, {"type": "object", "properties": {"cmdKey": {"type": ["string", "null"]}, "emitted": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "entity": {"type": ["string", "null"]}, "flow": {"type": ["string", "null"]}, "modified": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "pub": {"type": "boolean"}, "adsid": {"type": ["string", "null"]}, "antenna": {"type": ["integer", "null"]}, "autotestResult": {"type": ["string", "null"]}, "batteryLevel": {"type": "integer"}, "cpuLevel": {"type": "integer"}, "currentTime": {"type": ["string", "null"]}, "dateResetBattery": {"type": ["string", "null"]}, "firstUseDate": {"type": ["string", "null"]}, "flashTotal": {"type": "integer"}, "iccid": {"type": ["string", "null"]}, "imei": {"type": ["string", "null"]}, "ledGprsHigh": {"type": "integer"}, "ledGprsLow": {"type": "integer"}, "ledGprsMed": {"type": "integer"}, "ledLteHigh": {"type": "integer"}, "ledLteLow": {"type": "integer"}, "ledLteMed": {"type": "integer"}, "ledUmtsHigh": {"type": "integer"}, "ledUmtsLow": {"type": "integer"}, "ledUmtsMed": {"type": "integer"}, "ledLtemHigh": {"type": "integer"}, "ledLtemLow": {"type": "integer"}, "ledLtemMed": {"type": "integer"}, "ledNBIOTHigh": {"type": "integer"}, "ledNBIOTLow": {"type": "integer"}, "ledNBIOTMed": {"type": "integer"}, "manufacturer": {"type": ["string", "null"]}, "microCut": {"type": "integer"}, "partitions": {"type": ["array", "null"], "items": {"type": "object", "additionalProperties": false, "properties": {"free": {"type": "integer"}, "fs": {"type": ["string", "null"]}, "mount": {"type": ["string", "null"]}, "name": {"type": ["string", "null"]}, "size": {"type": "integer"}}}}, "productClass": {"type": ["string", "null"]}, "ramLevel": {"type": "integer"}, "ramTotal": {"type": "integer"}, "serialNumber": {"type": ["string", "null"]}, "supplyType": {"type": ["integer", "null"]}, "uartSpeed": {"type": "integer"}, "upTime": {"type": ["integer", "null"]}, "class": {"type": "string", "enum": ["Hardware"]}, "name": {"type": "string", "enum": ["hard"]}}, "required": ["name", "class"]}, {"type": "object", "properties": {"cmdKey": {"type": ["string", "null"]}, "emitted": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "entity": {"type": ["string", "null"]}, "flow": {"type": ["string", "null"]}, "modified": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "pub": {"type": "boolean"}, "type": {"type": ["string", "null"]}, "class": {"type": "string", "enum": ["<PERSON>er"]}, "name": {"type": "string", "enum": ["meter"]}}, "required": ["name", "class"]}, {"type": "object", "properties": {"cmdKey": {"type": ["string", "null"]}, "emitted": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "entity": {"type": ["string", "null"]}, "flow": {"type": ["string", "null"]}, "modified": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "pub": {"type": "boolean"}, "rscp": {"type": ["integer", "null"]}, "rsrp": {"type": ["integer", "null"]}, "rxLevel": {"type": ["integer", "null"]}, "techno": {"type": ["string", "null"]}, "class": {"type": "string", "enum": ["Modem"]}, "name": {"type": "string", "enum": ["modem"]}}, "required": ["name", "class"]}, {"type": "object", "properties": {"cmdKey": {"type": ["string", "null"]}, "emitted": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "entity": {"type": ["string", "null"]}, "flow": {"type": ["string", "null"]}, "modified": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "pub": {"type": "boolean"}, "count": {"type": "integer"}, "cplc": {"type": ["string", "null"]}, "secConfig": {"type": ["string", "null"]}, "securityMod": {"type": ["string", "null"]}, "class": {"type": "string", "enum": ["MS"]}, "name": {"type": "string", "enum": ["ms"]}}, "required": ["name", "class"]}, {"type": "object", "properties": {"cmdKey": {"type": ["string", "null"]}, "emitted": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "entity": {"type": ["string", "null"]}, "flow": {"type": ["string", "null"]}, "modified": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "pub": {"type": "boolean"}, "applications": {"type": ["array", "null"], "items": {"type": "object", "additionalProperties": false, "properties": {"installed": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "name": {"type": ["string", "null"]}, "version": {"type": ["string", "null"]}}}}, "initrd": {"type": "object", "additionalProperties": false, "properties": {"installed": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "name": {"type": ["string", "null"]}, "version": {"type": ["string", "null"]}}}, "kernel": {"type": "object", "additionalProperties": false, "properties": {"installed": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "name": {"type": ["string", "null"]}, "version": {"type": ["string", "null"]}}}, "rootfs": {"type": ["array", "null"], "items": {"type": "object", "additionalProperties": false, "properties": {"installed": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "name": {"type": ["string", "null"]}, "version": {"type": ["string", "null"]}}}}, "class": {"type": "string", "enum": ["Software"]}, "name": {"type": "string", "enum": ["soft"]}}, "required": ["name", "class"]}]}}}, "required": ["name", "class"]}, {"type": "object", "properties": {"cmdKey": {"type": ["string", "null"]}, "emitted": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "entity": {"type": ["string", "null"]}, "flow": {"type": ["string", "null"]}, "modified": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "pub": {"type": "boolean"}, "enable": {"type": "boolean"}, "ipv4": {"type": "object", "additionalProperties": false, "properties": {"enable": {"type": ["integer", "null"]}, "address": {"type": ["string", "null"]}, "subnet": {"type": ["string", "null"]}, "gateway": {"type": ["string", "null"]}, "addressingType": {"type": ["string", "null"]}}}, "ipv6": {"type": "object", "additionalProperties": false, "properties": {"enable": {"type": "integer"}, "address": {"type": ["string", "null"]}, "subnet": {"type": ["integer", "null"]}, "gateway": {"type": ["string", "null"]}, "addressingType": {"type": ["string", "null"]}}}, "reset": {"type": "boolean"}, "status": {"type": ["integer", "null"]}, "class": {"type": "string", "enum": ["IpSec"]}, "name": {"type": "string", "enum": ["ipsec"]}}, "required": ["name", "class"]}, {"type": "object", "properties": {"cmdKey": {"type": ["string", "null"]}, "emitted": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "entity": {"type": ["string", "null"]}, "flow": {"type": ["string", "null"]}, "modified": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "pub": {"type": "boolean"}, "apn": {"type": ["array", "null"], "items": {"type": "object", "additionalProperties": false, "properties": {"priority": {"type": "integer"}, "domain": {"type": ["string", "null"]}, "mcc": {"type": "string"}, "apname": {"type": "string"}, "mnc": {"type": "string"}}}}, "gzipLevel": {"type": "integer"}, "httpTmo": {"type": "integer"}, "linkType": {"type": "integer"}, "log": {"type": "object", "additionalProperties": false, "properties": {"depth": {"type": "integer"}, "dir": {"type": ["string", "null"]}, "level": {"type": "integer"}, "nb": {"type": "integer"}, "size": {"type": "integer"}}}, "nbBitsMasqueIPV4": {"type": "integer"}, "nbBitsMasqueIPV6": {"type": "integer"}, "pin": {"type": ["string", "null"]}, "pinEnabled": {"type": "boolean"}, "pingFreq": {"type": "integer"}, "pingIP1": {"type": ["array", "null"], "items": {"type": ["string", "null"]}}, "pingIP2": {"type": ["array", "null"], "items": {"type": ["string", "null"]}}, "pingTmo1": {"type": "integer"}, "pingTmo2": {"type": "integer"}, "reachable": {"type": "boolean"}, "resetTmo": {"type": "integer"}, "resetWanTmo": {"type": "integer"}, "retryFreq": {"type": "integer"}, "state": {"type": ["integer", "null"]}, "system": {"type": "integer"}, "tcpSynRetry": {"type": "integer"}, "validDate": {"type": ["string", "null"]}, "version": {"type": ["string", "null"]}, "class": {"type": "string", "enum": ["DM"]}, "name": {"type": "string", "enum": ["dm"]}}, "required": ["name", "class"]}, {"type": "object", "properties": {"cmdKey": {"type": ["string", "null"]}, "emitted": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "entity": {"type": ["string", "null"]}, "flow": {"type": ["string", "null"]}, "modified": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "pub": {"type": "boolean"}, "class": {"type": "string", "enum": ["Container"]}, "name": {"type": "string", "enum": ["flows"]}, "nb": {"type": "integer", "enum": [10]}, "objects": {"additionalItems": false, "items": {"anyOf": [{"type": "object", "properties": {"cmdKey": {"type": ["string", "null"]}, "emitted": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "entity": {"type": ["string", "null"]}, "flow": {"type": ["string", "null"]}, "modified": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "pub": {"type": "boolean"}, "freq1": {"type": "integer"}, "freq2": {"type": "integer"}, "lb": {"type": ["integer", "null"]}, "lbMax": {"type": ["integer", "null"]}, "retry1": {"type": "integer"}, "retry2": {"type": "integer"}, "slots": {"type": ["array", "null"], "items": {"type": "object", "additionalProperties": false, "properties": {"day": {"type": "integer"}, "duration": {"type": "integer"}, "time": {"type": ["string", "null"]}}}}, "url1": {"type": ["array", "null"], "items": {"type": ["string", "null"]}}, "url2": {"type": ["array", "null"], "items": {"type": ["string", "null"]}}, "class": {"type": "string", "enum": ["FlowOut"]}, "name": {"type": "string", "enum": ["alarm"]}}, "required": ["name", "class"]}, {"type": "object", "properties": {"cmdKey": {"type": ["string", "null"]}, "emitted": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "entity": {"type": ["string", "null"]}, "flow": {"type": ["string", "null"]}, "modified": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "pub": {"type": "boolean"}, "freq1": {"type": "integer"}, "freq2": {"type": "integer"}, "lb": {"type": ["integer", "null"]}, "lbMax": {"type": ["integer", "null"]}, "retry1": {"type": "integer"}, "retry2": {"type": "integer"}, "slots": {"type": ["array", "null"], "items": {"type": "object", "additionalProperties": false, "properties": {"day": {"type": "integer"}, "duration": {"type": "integer"}, "time": {"type": ["string", "null"]}}}}, "url1": {"type": ["array", "null"], "items": {"type": ["string", "null"]}}, "url2": {"type": ["array", "null"], "items": {"type": ["string", "null"]}}, "class": {"type": "string", "enum": ["FlowOut"]}, "name": {"type": "string", "enum": ["cfg"]}}, "required": ["name", "class"]}, {"type": "object", "properties": {"cmdKey": {"type": ["string", "null"]}, "emitted": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "entity": {"type": ["string", "null"]}, "flow": {"type": ["string", "null"]}, "modified": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "pub": {"type": "boolean"}, "freq1": {"type": "integer"}, "freq2": {"type": "integer"}, "lb": {"type": ["integer", "null"]}, "lbMax": {"type": ["integer", "null"]}, "retry1": {"type": "integer"}, "retry2": {"type": "integer"}, "slots": {"type": ["array", "null"], "items": {"type": "object", "additionalProperties": false, "properties": {"day": {"type": "integer"}, "duration": {"type": "integer"}, "time": {"type": ["string", "null"]}}}}, "url1": {"type": ["array", "null"], "items": {"type": ["string", "null"]}}, "url2": {"type": ["array", "null"], "items": {"type": ["string", "null"]}}, "class": {"type": "string", "enum": ["FlowOut"]}, "name": {"type": "string", "enum": ["command"]}}, "required": ["name", "class"]}, {"type": "object", "properties": {"cmdKey": {"type": ["string", "null"]}, "emitted": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "entity": {"type": ["string", "null"]}, "flow": {"type": ["string", "null"]}, "modified": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "pub": {"type": "boolean"}, "freq1": {"type": "integer"}, "freq2": {"type": "integer"}, "lb": {"type": ["integer", "null"]}, "lbMax": {"type": ["integer", "null"]}, "retry1": {"type": "integer"}, "retry2": {"type": "integer"}, "slots": {"type": ["array", "null"], "items": {"type": "object", "additionalProperties": false, "properties": {"day": {"type": "integer"}, "duration": {"type": "integer"}, "time": {"type": ["string", "null"]}}}}, "url1": {"type": ["array", "null"], "items": {"type": ["string", "null"]}}, "url2": {"type": ["array", "null"], "items": {"type": ["string", "null"]}}, "class": {"type": "string", "enum": ["FlowOut"]}, "name": {"type": "string", "enum": ["csr"]}}, "required": ["name", "class"]}, {"type": "object", "properties": {"cmdKey": {"type": ["string", "null"]}, "emitted": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "entity": {"type": ["string", "null"]}, "flow": {"type": ["string", "null"]}, "modified": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "pub": {"type": "boolean"}, "freq1": {"type": "integer"}, "freq2": {"type": "integer"}, "lb": {"type": ["integer", "null"]}, "lbMax": {"type": ["integer", "null"]}, "retry1": {"type": "integer"}, "retry2": {"type": "integer"}, "slots": {"type": ["array", "null"], "items": {"type": "object", "additionalProperties": false, "properties": {"day": {"type": "integer"}, "duration": {"type": "integer"}, "time": {"type": ["string", "null"]}}}}, "url1": {"type": ["array", "null"], "items": {"type": ["string", "null"]}}, "url2": {"type": ["array", "null"], "items": {"type": ["string", "null"]}}, "class": {"type": "string", "enum": ["FlowOut"]}, "name": {"type": "string", "enum": ["init"]}}, "required": ["name", "class"]}, {"type": "object", "properties": {"cmdKey": {"type": ["string", "null"]}, "emitted": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "entity": {"type": ["string", "null"]}, "flow": {"type": ["string", "null"]}, "modified": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "pub": {"type": "boolean"}, "freq1": {"type": "integer"}, "freq2": {"type": "integer"}, "lb": {"type": ["integer", "null"]}, "lbMax": {"type": ["integer", "null"]}, "retry1": {"type": "integer"}, "retry2": {"type": "integer"}, "slots": {"type": ["array", "null"], "items": {"type": "object", "additionalProperties": false, "properties": {"day": {"type": "integer"}, "duration": {"type": "integer"}, "time": {"type": ["string", "null"]}}}}, "url1": {"type": ["array", "null"], "items": {"type": ["string", "null"]}}, "url2": {"type": ["array", "null"], "items": {"type": ["string", "null"]}}, "class": {"type": "string", "enum": ["FlowOut"]}, "name": {"type": "string", "enum": ["ntp"]}}, "required": ["name", "class"]}, {"type": "object", "properties": {"cmdKey": {"type": ["string", "null"]}, "emitted": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "entity": {"type": ["string", "null"]}, "flow": {"type": ["string", "null"]}, "modified": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "pub": {"type": "boolean"}, "freq1": {"type": "integer"}, "freq2": {"type": "integer"}, "lb": {"type": ["integer", "null"]}, "lbMax": {"type": ["integer", "null"]}, "retry1": {"type": "integer"}, "retry2": {"type": "integer"}, "slots": {"type": ["array", "null"], "items": {"type": "object", "additionalProperties": false, "properties": {"day": {"type": "integer"}, "duration": {"type": "integer"}, "time": {"type": ["string", "null"]}}}}, "url1": {"type": ["array", "null"], "items": {"type": ["string", "null"]}}, "url2": {"type": ["array", "null"], "items": {"type": ["string", "null"]}}, "class": {"type": "string", "enum": ["FlowIn"]}, "name": {"type": "string", "enum": ["reachable"]}}, "required": ["name", "class"]}, {"type": "object", "properties": {"cmdKey": {"type": ["string", "null"]}, "emitted": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "entity": {"type": ["string", "null"]}, "flow": {"type": ["string", "null"]}, "modified": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "pub": {"type": "boolean"}, "freq1": {"type": "integer"}, "freq2": {"type": "integer"}, "lb": {"type": ["integer", "null"]}, "lbMax": {"type": ["integer", "null"]}, "retry1": {"type": "integer"}, "retry2": {"type": "integer"}, "slots": {"type": ["array", "null"], "items": {"type": "object", "additionalProperties": false, "properties": {"day": {"type": "integer"}, "duration": {"type": "integer"}, "time": {"type": ["string", "null"]}}}}, "url1": {"type": ["array", "null"], "items": {"type": ["string", "null"]}}, "url2": {"type": ["array", "null"], "items": {"type": ["string", "null"]}}, "class": {"type": "string", "enum": ["FlowOut"]}, "name": {"type": "string", "enum": ["stat"]}}, "required": ["name", "class"]}, {"type": "object", "properties": {"cmdKey": {"type": ["string", "null"]}, "emitted": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "entity": {"type": ["string", "null"]}, "flow": {"type": ["string", "null"]}, "modified": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "pub": {"type": "boolean"}, "freq1": {"type": "integer"}, "freq2": {"type": "integer"}, "lb": {"type": ["integer", "null"]}, "lbMax": {"type": ["integer", "null"]}, "retry1": {"type": "integer"}, "retry2": {"type": "integer"}, "slots": {"type": ["array", "null"], "items": {"type": "object", "additionalProperties": false, "properties": {"day": {"type": "integer"}, "duration": {"type": "integer"}, "time": {"type": ["string", "null"]}}}}, "url1": {"type": ["array", "null"], "items": {"type": ["string", "null"]}}, "url2": {"type": ["array", "null"], "items": {"type": ["string", "null"]}}, "class": {"type": "string", "enum": ["FlowOut"]}, "name": {"type": "string", "enum": ["upgrade"]}}, "required": ["name", "class"]}, {"type": "object", "properties": {"cmdKey": {"type": ["string", "null"]}, "emitted": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "entity": {"type": ["string", "null"]}, "flow": {"type": ["string", "null"]}, "modified": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "pub": {"type": "boolean"}, "freq1": {"type": "integer"}, "freq2": {"type": "integer"}, "lb": {"type": ["integer", "null"]}, "lbMax": {"type": ["integer", "null"]}, "retry1": {"type": "integer"}, "retry2": {"type": "integer"}, "slots": {"type": ["array", "null"], "items": {"type": "object", "additionalProperties": false, "properties": {"day": {"type": "integer"}, "duration": {"type": "integer"}, "time": {"type": ["string", "null"]}}}}, "url1": {"type": ["array", "null"], "items": {"type": ["string", "null"]}}, "url2": {"type": ["array", "null"], "items": {"type": ["string", "null"]}}, "class": {"type": "string", "enum": ["FlowOut"]}, "name": {"type": "string", "enum": ["waitprog"]}}, "required": ["name", "class"]}]}}}, "required": ["name", "class"]}, {"type": "object", "properties": {"cmdKey": {"type": ["string", "null"]}, "emitted": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "entity": {"type": ["string", "null"]}, "flow": {"type": ["string", "null"]}, "modified": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "pub": {"type": "boolean"}, "authentication": {"type": "integer"}, "currentMRU": {"type": ["integer", "null"]}, "enable": {"type": "boolean"}, "ipcpEnable": {"type": "boolean"}, "ipv4": {"type": "object", "additionalProperties": false, "properties": {"enable": {"type": "integer"}, "address": {"type": ["string", "null"]}, "subnet": {"type": ["string", "null"]}, "gateway": {"type": ["string", "null"]}, "addressingType": {"type": ["string", "null"]}}}, "ipv6": {"type": "object", "additionalProperties": false, "properties": {"enable": {"type": "integer"}, "address": {"type": ["string", "null"]}, "subnet": {"type": ["integer", "null"]}, "gateway": {"type": ["string", "null"]}, "addressingType": {"type": ["string", "null"]}}}, "maxMRU": {"type": "integer"}, "maxMTU": {"type": "integer"}, "password": {"type": ["string", "null"]}, "reset": {"type": "boolean"}, "status": {"type": ["integer", "null"]}, "username": {"type": ["string", "null"]}, "class": {"type": "string", "enum": ["PPP"]}, "name": {"type": "string", "enum": ["ppp"]}}, "required": ["name", "class"]}, {"type": "object", "properties": {"class": {"type": "string", "enum": ["Container"]}, "name": {"type": "string", "enum": ["properties"]}, "nb": {"type": "integer"}}, "required": ["name", "class"]}, {"type": "object", "properties": {"cmdKey": {"type": ["string", "null"]}, "emitted": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "entity": {"type": ["string", "null"]}, "flow": {"type": ["string", "null"]}, "modified": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "pub": {"type": "boolean"}, "class": {"type": "string", "enum": ["Container"]}, "name": {"type": "string", "enum": ["services"]}, "nb": {"type": "integer", "enum": [8, 9, 10, 11, 12]}, "objects": {"additionalItems": false, "items": {"anyOf": [{"type": "object", "properties": {"cmdKey": {"type": ["string", "null"]}, "emitted": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "entity": {"type": ["string", "null"]}, "flow": {"type": ["string", "null"]}, "modified": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "pub": {"type": "boolean"}, "status": {"type": ["string", "null"], "enum": ["STARTED", "STOPPED", "STARTING", "STOPPING"]}, "started": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "stopped": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "request": {"type": ["string", "null"], "enum": ["START", "STOP", "", null]}, "result": {"type": "integer"}, "error": {"type": ["string", "null"]}, "autostart": {"type": "boolean"}, "autostop": {"type": "integer"}, "log": {"type": "object", "additionalProperties": false, "properties": {"depth": {"type": "integer"}, "dir": {"type": "string"}, "level": {"type": "integer"}, "nb": {"type": "integer"}, "size": {"type": "integer"}}}, "authMtc": {"type": "boolean"}, "authSms": {"type": "boolean"}, "mtcCallback": {"type": "integer"}, "smsCallback": {"type": "integer"}, "whiteList": {"type": ["array", "null"], "items": {"type": "string"}}, "class": {"type": "string", "enum": ["Srv<PERSON><PERSON><PERSON>"]}, "name": {"type": "string", "enum": ["callback"]}}, "required": ["name", "class"]}, {"type": "object", "properties": {"cmdKey": {"type": ["string", "null"]}, "emitted": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "entity": {"type": ["string", "null"]}, "flow": {"type": ["string", "null"]}, "modified": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "pub": {"type": "boolean"}, "status": {"type": ["string", "null"], "enum": ["STARTED", "STOPPED", "STARTING", "STOPPING"]}, "started": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "stopped": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "request": {"type": ["string", "null"], "enum": ["START", "STOP", "", null]}, "result": {"type": "integer"}, "error": {"type": ["string", "null"]}, "autostart": {"type": "boolean"}, "autostop": {"type": "integer"}, "log": {"type": "object", "additionalProperties": false, "properties": {"depth": {"type": "integer"}, "dir": {"type": "string"}, "level": {"type": "integer"}, "nb": {"type": "integer"}, "size": {"type": "integer"}}}, "class": {"type": "string", "enum": ["SrvLog"]}, "name": {"type": "string", "enum": ["log"]}}, "required": ["name", "class"]}, {"type": "object", "properties": {"cmdKey": {"type": ["string", "null"]}, "emitted": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "entity": {"type": ["string", "null"]}, "flow": {"type": ["string", "null"]}, "modified": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "pub": {"type": "boolean"}, "status": {"type": ["string", "null"], "enum": ["STARTED", "STOPPED", "STARTING", "STOPPING"]}, "started": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "stopped": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "request": {"type": ["string", "null"], "enum": ["START", "STOP", "", null]}, "result": {"type": "integer"}, "error": {"type": ["string", "null"]}, "autostart": {"type": "boolean"}, "autostop": {"type": "integer"}, "log": {"type": "object", "additionalProperties": false, "properties": {"depth": {"type": "integer"}, "dir": {"type": "string"}, "level": {"type": "integer"}, "nb": {"type": "integer"}, "size": {"type": "integer"}}}, "alarmsOn": {"type": "integer"}, "alarmsStat": {"type": ["string", "null"]}, "batteryDepth": {"type": "integer"}, "batteryFreq": {"type": "integer"}, "batteryOn": {"type": "integer"}, "batteryStat": {"type": ["string", "null"]}, "bootOn": {"type": "integer"}, "bootStat": {"type": ["string", "null"]}, "boxOn": {"type": "integer"}, "boxStat": {"type": ["string", "null"]}, "cellsFreq": {"type": "integer"}, "cellsInitFreq": {"type": "integer"}, "cellsInitNbSamples": {"type": "integer"}, "cellsNeighbours": {"type": "integer"}, "cellsOn": {"type": "integer"}, "cellsStat": {"type": ["string", "null"]}, "clockOn": {"type": "integer"}, "clockStat": {"type": ["string", "null"]}, "cpuFreq": {"type": "integer"}, "cpuOn": {"type": "integer"}, "cpuStat": {"type": ["string", "null"]}, "doorOn": {"type": "integer"}, "doorStat": {"type": ["string", "null"]}, "ethernetOn": {"type": "integer"}, "ethernetStat": {"type": ["string", "null"]}, "flashOn": {"type": "integer"}, "flashStat": {"type": ["string", "null"]}, "fsFreq": {"type": "integer"}, "fsOn": {"type": "integer"}, "fsStat": {"type": ["string", "null"]}, "logOn": {"type": "integer"}, "logStat": {"type": ["string", "null"]}, "meterOn": {"type": "integer"}, "meterStat": {"type": ["string", "null"]}, "msOn": {"type": "integer"}, "msStat": {"type": ["string", "null"]}, "networkDepth": {"type": "integer"}, "networkOn": {"type": "integer"}, "networkStat": {"type": ["string", "null"]}, "ramFreq": {"type": "integer"}, "ramOn": {"type": "integer"}, "ramStat": {"type": ["string", "null"]}, "securityOn": {"type": "integer"}, "securityStat": {"type": ["string", "null"]}, "simOn": {"type": "integer"}, "simStat": {"type": ["string", "null"]}, "supplyOn": {"type": "integer"}, "supplyStat": {"type": ["string", "null"]}, "ttl": {"type": "integer"}, "wanDepth": {"type": "integer"}, "wanOn": {"type": "integer"}, "wanStat": {"type": ["string", "null"]}, "class": {"type": "string", "enum": ["SrvMonitoring"]}, "name": {"type": "string", "enum": ["monitoring"]}}, "required": ["name", "class"]}, {"type": "object", "properties": {"cmdKey": {"type": ["string", "null"]}, "emitted": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "entity": {"type": ["string", "null"]}, "flow": {"type": ["string", "null"]}, "modified": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "pub": {"type": "boolean"}, "status": {"type": ["string", "null"], "enum": ["STARTED", "STOPPED", "STARTING", "STOPPING"]}, "started": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "stopped": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "request": {"type": ["string", "null"], "enum": ["START", "STOP", "", null]}, "result": {"type": "integer"}, "error": {"type": ["string", "null"]}, "autostart": {"type": "boolean"}, "autostop": {"type": "integer"}, "log": {"type": "object", "additionalProperties": false, "properties": {"depth": {"type": "integer"}, "dir": {"type": "string"}, "level": {"type": "integer"}, "nb": {"type": "integer"}, "size": {"type": "integer"}}}, "allowUsers": {"type": ["string", "null"]}, "deactivation": {"type": ["string", "null"]}, "listenAddress": {"type": ["array", "null"], "items": {"type": "string"}}, "port": {"type": "integer"}, "sshFingerprint": {"type": ["string", "null"]}, "sshFingerprintType": {"type": "integer"}, "sshPublicKeyType": {"type": "integer"}, "umask": {"type": ["string", "null"]}, "class": {"type": "string", "enum": ["SrvSsh"]}, "name": {"type": "string", "enum": ["ssh"]}}, "required": ["name", "class"]}, {"type": "object", "properties": {"cmdKey": {"type": ["string", "null"]}, "emitted": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "entity": {"type": ["string", "null"]}, "flow": {"type": ["string", "null"]}, "modified": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "pub": {"type": "boolean"}, "status": {"type": ["string", "null"], "enum": ["STARTED", "STOPPED", "STARTING", "STOPPING"]}, "started": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "stopped": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "request": {"type": ["string", "null"], "enum": ["START", "STOP", "", null]}, "result": {"type": "integer"}, "error": {"type": ["string", "null"]}, "autostart": {"type": "boolean"}, "autostop": {"type": "integer"}, "log": {"type": "object", "additionalProperties": false, "properties": {"depth": {"type": "integer"}, "dir": {"type": "string"}, "level": {"type": "integer"}, "nb": {"type": "integer"}, "size": {"type": "integer"}}}, "authKOAlarm": {"type": ["string", "null"]}, "authKODelay": {"type": "integer"}, "authKOLim": {"type": "integer"}, "authKOOn": {"type": "integer"}, "batteryAlarm": {"type": ["string", "null"]}, "batteryDelay": {"type": "integer"}, "batteryInf": {"type": "integer"}, "batteryLim": {"type": "integer"}, "batteryOn": {"type": "integer"}, "batterySup": {"type": "integer"}, "bootAlarm": {"type": ["string", "null"]}, "bootCause": {"type": "integer"}, "bootDelay": {"type": "integer"}, "bootLim": {"type": "integer"}, "bootOn": {"type": "integer"}, "boxAlarm": {"type": ["string", "null"]}, "boxDelay": {"type": "integer"}, "boxLim": {"type": "integer"}, "boxOn": {"type": "integer"}, "certInvalidAlarm": {"type": ["string", "null"]}, "certInvalidDelay": {"type": "integer"}, "certInvalidLim": {"type": "integer"}, "certInvalidOn": {"type": "integer"}, "certTTLAlarm": {"type": ["string", "null"]}, "certTTLDelay": {"type": "integer"}, "certTTLLim": {"type": "integer"}, "certTTLOn": {"type": "integer"}, "cpuAlarm": {"type": ["string", "null"]}, "cpuDelay": {"type": "integer"}, "cpuInf": {"type": "integer"}, "cpuLim": {"type": "integer"}, "cpuOn": {"type": "integer"}, "cpuSup": {"type": "integer"}, "doorAlarm": {"type": ["string", "null"]}, "doorDelay": {"type": "integer"}, "doorLim": {"type": "integer"}, "doorOn": {"type": "integer"}, "fileIntegrityKOAlarm": {"type": ["string", "null"]}, "fileIntegrityKODelay": {"type": "integer"}, "fileIntegrityKOLim": {"type": "integer"}, "fileIntegrityKOOn": {"type": "integer"}, "fileServerKOAlarm": {"type": ["string", "null"]}, "fileServerKODelay": {"type": "integer"}, "fileServerKOLim": {"type": "integer"}, "fileServerKOOn": {"type": "integer"}, "fsAlarm": {"type": ["string", "null"]}, "fsDelay": {"type": "integer"}, "fsInf": {"type": "integer"}, "fsLim": {"type": "integer"}, "fsOn": {"type": "integer"}, "fsSup": {"type": "integer"}, "keyKOAlarm": {"type": ["string", "null"]}, "keyKODelay": {"type": "integer"}, "keyKOLim": {"type": "integer"}, "keyKOOn": {"type": "integer"}, "meterAlarm": {"type": ["string", "null"]}, "meterDelay": {"type": "integer"}, "meterLim": {"type": "integer"}, "meterOn": {"type": "integer"}, "msAdminAlarm": {"type": ["string", "null"]}, "msAdminDelay": {"type": "integer"}, "msAdminLim": {"type": "integer"}, "msAdminOn": {"type": "integer"}, "ntpAlarm": {"type": ["string", "null"]}, "ntpDelay": {"type": "integer"}, "ntpLim": {"type": "integer"}, "ntpOn": {"type": "integer"}, "ramAlarm": {"type": ["string", "null"]}, "ramDelay": {"type": "integer"}, "ramInf": {"type": "integer"}, "ramLim": {"type": "integer"}, "ramOn": {"type": "integer"}, "ramSup": {"type": "integer"}, "restoredAlarm": {"type": ["string", "null"]}, "restoredDelay": {"type": "integer"}, "restoredLim": {"type": "integer"}, "restoredOn": {"type": "integer"}, "simAlarm": {"type": ["string", "null"]}, "simDelay": {"type": "integer"}, "simLim": {"type": "integer"}, "simOn": {"type": "integer"}, "sshAlarm": {"type": ["string", "null"]}, "sshDelay": {"type": "integer"}, "sshLim": {"type": "integer"}, "sshOn": {"type": "integer"}, "supplyAlarm": {"type": ["string", "null"]}, "supplyDelay": {"type": "integer"}, "supplyLim": {"type": "integer"}, "supplyOn": {"type": "integer"}, "ttl": {"type": "integer"}, "temperatureAlarm": {"type": ["string", "null"]}, "temperatureDelay": {"type": "integer"}, "temperatureLim": {"type": "integer"}, "temperatureOn": {"type": "integer"}, "class": {"type": "string", "enum": ["SrvSupervision"]}, "name": {"type": "string", "enum": ["supervision"]}}, "required": ["name", "class"]}, {"type": "object", "properties": {"cmdKey": {"type": ["string", "null"]}, "emitted": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "entity": {"type": ["string", "null"]}, "flow": {"type": ["string", "null"]}, "modified": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "lastSynchro": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "pub": {"type": "boolean"}, "status": {"type": ["string", "null"], "enum": ["STARTED", "STOPPED", "STARTING", "STOPPING"]}, "started": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "stopped": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "request": {"type": ["string", "null"], "enum": ["START", "STOP", "", null]}, "result": {"type": "integer"}, "error": {"type": ["string", "null"]}, "autostart": {"type": "boolean"}, "autostop": {"type": "integer"}, "log": {"type": "object", "additionalProperties": false, "properties": {"depth": {"type": "integer"}, "dir": {"type": "string"}, "level": {"type": "integer"}, "nb": {"type": "integer"}, "size": {"type": "integer"}}}, "nbFailure": {"type": "integer"}, "ntpflow": {"type": ["string", "null"]}, "timeZone": {"type": ["string", "null"]}, "class": {"type": "string", "enum": ["SrvTime"]}, "name": {"type": "string", "enum": ["time"]}}, "required": ["name", "class"]}, {"type": "object", "properties": {"cmdKey": {"type": ["string", "null"]}, "emitted": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "entity": {"type": ["string", "null"]}, "flow": {"type": ["string", "null"]}, "modified": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "pub": {"type": "boolean"}, "status": {"type": ["string", "null"], "enum": ["STARTED", "STOPPED", "STARTING", "STOPPING"]}, "started": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "stopped": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "request": {"type": ["string", "null"], "enum": ["START", "STOP", "", null]}, "result": {"type": "integer"}, "error": {"type": ["string", "null"]}, "autostart": {"type": "boolean"}, "autostop": {"type": "integer"}, "log": {"type": "object", "additionalProperties": false, "properties": {"depth": {"type": "integer"}, "dir": {"type": "string"}, "level": {"type": "integer"}, "nb": {"type": "integer"}, "size": {"type": "integer"}}}, "ttl": {"type": "integer"}, "class": {"type": "string", "enum": ["SrvUpgrade"]}, "name": {"type": "string", "enum": ["upgrade"]}}, "required": ["name", "class"]}, {"type": "object", "properties": {"cmdKey": {"type": ["string", "null"]}, "emitted": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "entity": {"type": ["string", "null"]}, "flow": {"type": ["string", "null"]}, "modified": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "pub": {"type": "boolean"}, "status": {"type": ["string", "null"], "enum": ["STARTED", "STOPPED", "STARTING", "STOPPING"]}, "started": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "stopped": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "request": {"type": ["string", "null"], "enum": ["START", "STOP", "", null]}, "result": {"type": "integer"}, "error": {"type": ["string", "null"]}, "autostart": {"type": "boolean"}, "autostop": {"type": "integer"}, "log": {"type": "object", "additionalProperties": false, "properties": {"depth": {"type": "integer"}, "dir": {"type": "string"}, "level": {"type": "integer"}, "nb": {"type": "integer"}, "size": {"type": "integer"}}}, "ttl": {"type": "integer"}, "class": {"type": "string", "enum": ["SrvWeb"]}, "name": {"type": "string", "enum": ["web"]}}, "required": ["name", "class"]}, {"type": "object", "properties": {"cmdKey": {"type": ["string", "null"]}, "emitted": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "entity": {"type": ["string", "null"]}, "flow": {"type": ["string", "null"]}, "modified": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "pub": {"type": "boolean"}, "status": {"type": ["string", "null"], "enum": ["STARTED", "STOPPED", "STARTING", "STOPPING"]}, "started": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "stopped": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "request": {"type": ["string", "null"], "enum": ["START", "STOP", "", null]}, "result": {"type": "integer"}, "error": {"type": ["string", "null"]}, "autostart": {"type": "boolean"}, "autostop": {"type": "integer"}, "log": {"type": "object", "additionalProperties": false, "properties": {"depth": {"type": "integer"}, "dir": {"type": "string"}, "level": {"type": "integer"}, "nb": {"type": "integer"}, "size": {"type": "integer"}}}, "class": {"type": "string", "enum": ["ManagedService", "<PERSON>v<PERSON><PERSON><PERSON><PERSON>"]}, "name": {"type": "string", "enum": ["passerelleICE", "PasserelleICE", "passerellePMEPMI", "passerelleSAPHIR", "applicatifMetier"]}}, "required": ["name", "class"]}]}}}, "required": ["name", "class"]}]}}}}