# Avec VsCode, utiliser l'extension "REST Client" - Natif avec Intellij Ultimate
### Reception de la configuration d'un boitier

# curl -X 'PUT'
#  'http://localhost:8443/SIService/475000DE007863490358606200DA61CD/db/cfg'
#  -H 'accept: text/plain'
#  -H 'Content-Type: application/json'
#  -d '{"cmdKey":null,"emitted":null,"entity":null,"flow":null,"modified":null,"pub":null,"certificate":{"cmdKey":null,"emitted":null,"entity":null,"flow":null,"modified":null,"pub":null,"lastValidityCheck":"2025-05-19T16:08:56Z","nextValidityCheck":null,"notAfter":null,"ttl":null},"device":{"cmdKey":null,"emitted":null,"entity":null,"flow":null,"modified":null,"pub":null,"box":null,"ethernet":null,"hard":{"cmdKey":null,"emitted":null,"entity":null,"flow":null,"modified":null,"pub":null,"ledLteLow":null,"ledLteMed":null,"ledLteHigh":null,"adsid":null,"antenna":null,"autotestResult":null,"batteryLevel":0,"cpuLevel":0,"currentTime":null,"dateResetBattery":null,"firstUseDate":null,"flashTotal":0,"iccid":null,"imei":null,"ledGprsHigh":0,"ledGprsLow":0,"ledGprsMed":0,"ledUmtsHigh":0,"ledUmtsLow":0,"ledUmtsMed":0,"manufacturer":null,"microCut":0,"partitions":null,"productClass":null,"ramLevel":0,"ramTotal":0,"serialNumber":null,"supplyType":null,"uartSpeed":0,"upTime":null},"meter":null,"modem":null,"ms":null,"soft":null},"dm":{"cmdKey":null,"emitted":null,"entity":null,"flow":null,"modified":null,"pub":null,"apn":null,"gzipLevel":0,"httpTmo":0,"linkType":0,"log":{"depth":0,"dir":null,"level":8,"nb":0,"size":0},"nbBitsMasqueIPV4":24,"nbBitsMasqueIPV6":0,"pin":null,"pinEnabled":false,"pingFreq":3,"pingIP1":["***********"],"pingIP2":["************"],"pingTmo1":5,"pingTmo2":5,"reachable":false,"resetTmo":0,"resetWanTmo":0,"retryFreq":0,"state":null,"system":0,"tcpSynRetry":0,"validDate":null,"version":null,"hash":"d41d8cd98f00b204e9800998ecf8427e"},"flows":null,"ppp":null,"services":null,"ipSec":null}'
PUT http://localhost:8443/SIService/475000DE007863490358606200DA61CD/db/cfg
accept: text/plain
Content-Type: application/json

{"cmdKey":null,"emitted":null,"entity":null,"flow":null,"modified":null,"pub":null,"certificate":{"cmdKey":null,"emitted":null,"entity":null,"flow":null,"modified":null,"pub":null,"lastValidityCheck":"2025-05-19T16:08:56Z","nextValidityCheck":null,"notAfter":null,"ttl":null},"device":{"cmdKey":null,"emitted":null,"entity":null,"flow":null,"modified":null,"pub":null,"box":null,"ethernet":null,"hard":{"cmdKey":null,"emitted":null,"entity":null,"flow":null,"modified":null,"pub":null,"ledLteLow":null,"ledLteMed":null,"ledLteHigh":null,"adsid":"adsDeTestI2R","antenna":null,"autotestResult":null,"batteryLevel":0,"cpuLevel":0,"currentTime":null,"dateResetBattery":null,"firstUseDate":null,"flashTotal":0,"iccid":null,"imei":null,"ledGprsHigh":0,"ledGprsLow":0,"ledGprsMed":0,"ledUmtsHigh":0,"ledUmtsLow":0,"ledUmtsMed":0,"manufacturer":null,"microCut":0,"partitions":null,"productClass":null,"ramLevel":0,"ramTotal":0,"serialNumber":null,"supplyType":null,"uartSpeed":0,"upTime":null},"meter":null,"modem":null,"ms":null,"soft":null},"dm":{"cmdKey":null,"emitted":null,"entity":null,"flow":null,"modified":null,"pub":null,"apn":null,"gzipLevel":0,"httpTmo":0,"linkType":0,"log":{"depth":0,"dir":null,"level":8,"nb":0,"size":0},"nbBitsMasqueIPV4":24,"nbBitsMasqueIPV6":0,"pin":null,"pinEnabled":false,"pingFreq":3,"pingIP1":["***********"],"pingIP2":["************"],"pingTmo1":5,"pingTmo2":5,"reachable":false,"resetTmo":0,"resetWanTmo":0,"retryFreq":0,"state":null,"system":0,"tcpSynRetry":0,"validDate":null,"version":null,"hash":"d41d8cd98f00b204e9800998ecf8427e"},"flows":null,"ppp":null,"services":null,"ipSec":null}

> {% client.test("Code de retour doit être 200", function () {
        client.assert(response.status === 200, "Statut attendu 200");
    }) %}

### Reception d'une configuration non valide

# curl -X 'PUT'
#  'http://localhost:8443/SIService/475000DE007863490358606200DA61CD/db/cfg'
#  -H 'accept: text/plain'
#  -H 'Content-Type: application/json'
#  -d '{"cmdKey":null,"emitted":null,"entity":null,"flow":null,"modified":null,"pub":null,"certificate":{"cmdKey":null,"emitted":null,"entity":null,"flow":null,"modified":null,"pub":null,"lastValidityCheck":"2025-05-19T16:08:56Z","nextValidityCheck":null,"notAfter":null,"ttl":null},"device":{"cmdKey":null,"emitted":null,"entity":null,"flow":null,"modified":null,"pub":null,"box":null,"ethernet":null,"hard":{"cmdKey":null,"emitted":null,"entity":null,"flow":null,"modified":null,"pub":null,"ledLteLow":null,"ledLteMed":null,"ledLteHigh":null,"adsid":null,"antenna":null,"autotestResult":null,"batteryLevel":0,"cpuLevel":0,"currentTime":null,"dateResetBattery":null,"firstUseDate":null,"flashTotal":0,"iccid":null,"imei":null,"ledGprsHigh":0,"ledGprsLow":0,"ledGprsMed":0,"ledUmtsHigh":0,"ledUmtsLow":0,"ledUmtsMed":0,"manufacturer":null,"microCut":0,"partitions":null,"productClass":null,"ramLevel":0,"ramTotal":0,"serialNumber":null,"supplyType":null,"uartSpeed":0,"upTime":null},"meter":null,"modem":null,"ms":null,"soft":null},"dm":{"cmdKey":null,"emitted":null,"entity":null,"flow":null,"modified":null,"pub":null,"apn":null,"gzipLevel":0,"httpTmo":0,"linkType":0,"log":{"depth":0,"dir":null,"level":8,"nb":0,"size":0},"nbBitsMasqueIPV4":24,"nbBitsMasqueIPV6":0,"pin":null,"pinEnabled":false,"pingFreq":3,"pingIP1":null,"pingIP2":null,"pingTmo1":5,"pingTmo2":5,"reachable":false,"resetTmo":0,"resetWanTmo":0,"retryFreq":0,"state":null,"system":0,"tcpSynRetry":0,"validDate":null,"version":null,"hash":"d41d8cd98f00b204e9800998ecf8427e"},"flows":null,"ppp":null,"services":null,"ipSec":null}'
PUT http://localhost:8443/SIService/475000DE007863490358606200DA61CD/db/cfg
accept: text/plain
Content-Type: application/json

{"cmdKey":null,"emitted":null,"entity":null,"flow":null,"modified":null,"pub":null,"certificate":{"cmdKey":null,"emitted":null,"entity":null,"flow":null,"modified":null,"pub":null,"lastValidityCheck":"2025-05-19T16:08:56Z","nextValidityCheck":null,"notAfter":null,"ttl":null},"device":{"cmdKey":null,"emitted":null,"entity":null,"flow":null,"modified":null,"pub":null,"box":null,"ethernet":null,"hard":{"cmdKey":null,"emitted":null,"entity":null,"flow":null,"modified":null,"pub":null,"ledLteLow":null,"ledLteMed":null,"ledLteHigh":null,"adsid":null,"antenna":null,"autotestResult":null,"batteryLevel":0,"cpuLevel":0,"currentTime":null,"dateResetBattery":null,"firstUseDate":null,"flashTotal":0,"iccid":null,"imei":null,"ledGprsHigh":0,"ledGprsLow":0,"ledGprsMed":0,"ledUmtsHigh":0,"ledUmtsLow":0,"ledUmtsMed":0,"manufacturer":null,"microCut":0,"partitions":null,"productClass":null,"ramLevel":0,"ramTotal":0,"serialNumber":null,"supplyType":null,"uartSpeed":0,"upTime":null},"meter":null,"modem":null,"ms":null,"soft":null},"dm":{"cmdKey":null,"emitted":null,"entity":null,"flow":null,"modified":null,"pub":null,"apn":null,"gzipLevel":0,"httpTmo":0,"linkType":0,"log":{"depth":0,"dir":null,"level":8,"nb":0,"size":0},"nbBitsMasqueIPV4":24,"nbBitsMasqueIPV6":0,"pin":null,"pinEnabled":false,"pingFreq":3,"pingIP1":null,"pingIP2":null,"pingTmo1":5,"pingTmo2":5,"reachable":false,"resetTmo":0,"resetWanTmo":0,"retryFreq":0,"state":null,"system":0,"tcpSynRetry":0,"validDate":null,"version":null,"hash":"d41d8cd98f00b204e9800998ecf8427e"},"flows":null,"ppp":null,"services":null,"ipSec":null}

> {% client.test("Code de retour doit être 400", function () {
        client.assert(response.status === 400, "Statut attendu 400");
    }) %}
###

