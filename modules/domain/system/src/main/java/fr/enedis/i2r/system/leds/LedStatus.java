package fr.enedis.i2r.system.leds;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

public enum LedStatus {
    OFF(0, 8, 16, 24),
    SLOW_BLINK(1),
    BLINK(2),
    FAST_BLINK(3),
    ON(4),
    BLINK_GREEN(10),
    ON_GREEN(12),
    BLINK_YELLOW(18),
    ON_YELLOW(20),
    BLINK_RED(26),
    ON_RED(28);

    private final byte value;
    private final List<Byte> offValues;

    LedStatus(Integer... values) {
        if (values.length == 1) {
            this.value = values[0].byteValue();
            this.offValues = Collections.emptyList();
        } else {
            this.value = 0;
            this.offValues = Collections.unmodifiableList(Stream.of(values)
                    .map(Integer::byteValue)
                    .toList());
        }
    }

    public byte getValue() {
        return value;
    }

    public List<Byte> getOffValues() {
        return offValues;
    }

    public static Optional<LedStatus> fromByte(byte value) {
        Optional<LedStatus> result = Optional.empty();
        for (LedStatus status : LedStatus.values()) {
            if (status.value == value || status.offValues.contains(value)) {
                result = Optional.of(status);
            }
        }
        return result;
    }
}
