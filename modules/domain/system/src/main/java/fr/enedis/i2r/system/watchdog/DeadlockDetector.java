package fr.enedis.i2r.system.watchdog;

import java.lang.management.ManagementFactory;
import java.lang.management.ThreadInfo;
import java.lang.management.ThreadMXBean;
import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
public class DeadlockDetector {

    private static final Logger logger = LoggerFactory.getLogger(DeadlockDetector.class);
    private final ThreadMXBean threadBean;
    private final DeadlockHandler deadlockHandler;

    public DeadlockDetector() {
        this(ManagementFactory.getThreadMXBean(), createDefaultDeadlockHandler());
    }

    public DeadlockDetector(ThreadMXBean threadBean, DeadlockHandler deadlockHandler) {
        this.threadBean = threadBean;
        this.deadlockHandler = deadlockHandler;
    }

    private static DeadlockHandler createDefaultDeadlockHandler() {
        return deadlockedThreads -> {
            Logger logger = LoggerFactory.getLogger(DeadlockDetector.class);
            logger.error("CRITICAL: Triggering System.exit(1) due to deadlock involving {} threads", deadlockedThreads != null ? deadlockedThreads.length : 0);
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            System.exit(1);
        };
    }

    public void checkAndHandleDeadlocks() {
        Optional<long[]> deadlockedThreads = Optional.ofNullable(threadBean.findDeadlockedThreads());
        deadlockedThreads.ifPresent((threads) -> {
            logger.error("DEADLOCK DETECTED! {} threads involved: {}", threads.length, Arrays.toString(threads));

            ThreadInfo[] threadInfos = threadBean.getThreadInfo(threads);
            Arrays.stream(threadInfos)
                .filter(Objects::nonNull)
                .forEach(threadInfo -> {
                    logger.error("Deadlocked thread: {} (ID: {}) - State: {} - Blocked on: {}",
                        threadInfo.getThreadName(),
                        threadInfo.getThreadId(),
                        threadInfo.getThreadState(),
                        threadInfo.getLockName());
            });

            logger.error("CRITICAL: Initiating restart due to deadlock");
            deadlockHandler.handle(threadInfos);
        });
    }
}
