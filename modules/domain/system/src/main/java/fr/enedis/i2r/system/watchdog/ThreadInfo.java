package fr.enedis.i2r.system.watchdog;

import java.time.Instant;
import java.util.concurrent.ScheduledFuture;

public class ThreadInfo {
    private final Thread thread;
    private volatile Instant lastHeartbeat;
    private volatile ScheduledFuture<?> scheduledHeartbeat;

    ThreadInfo(Thread thread, Instant lastHeartbeat) {
        this.thread = thread;
        this.lastHeartbeat = lastHeartbeat;
    }

    public Thread getThread() {
        return thread;
    }

    public Instant getLastHeartbeat() {
        return lastHeartbeat;
    }

    public void setLastHeartbeat(Instant lastHeartbeat) {
        this.lastHeartbeat = lastHeartbeat;
    }

    public ScheduledFuture<?> getScheduledHeartbeat() {
        return scheduledHeartbeat;
    }

    public void setScheduledHeartbeat(ScheduledFuture<?> scheduledHeartbeat) {
        this.scheduledHeartbeat = scheduledHeartbeat;
    }
}
