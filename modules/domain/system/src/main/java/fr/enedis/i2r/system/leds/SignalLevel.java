package fr.enedis.i2r.system.leds;

public enum SignalLevel {

    NO_SIGNAL(99, 99, LedStatus.OFF),
    VERY_POOR(0, 0, LedStatus.BLINK_YELLOW),
    POOR(1, 1, LedStatus.ON_YELLOW),
    GOOD(2, 30, LedStatus.BLINK_GREEN),
    EXCELLENT(31, 31, LedStatus.ON_GREEN);

    private final Integer minRSSI;
    private final Integer maxRSSI;
    private final LedStatus ledStatus;

    SignalLevel(Integer minRSSI, Integer maxRSSI, LedStatus ledStatus) {
        this.minRSSI = minRSSI;
        this.maxRSSI = maxRSSI;
        this.ledStatus = ledStatus;
    }

    public static SignalLevel fromRSSI(int rssi) {
        SignalLevel result = SignalLevel.NO_SIGNAL;
        for (SignalLevel level : SignalLevel.values()) {
            if (rssi >= level.minRSSI && rssi <= level.maxRSSI) {
                result = level;
                break;
            }
        }
        return result;
    }

    public LedStatus toLedStatus() {
        return ledStatus;
    }

}
