package fr.enedis.i2r.system;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.LoggerContext;
import fr.enedis.i2r.system.ports.SystemParametersPort;

public class LoggingLoader {

    private static final Logger logger = LoggerFactory.getLogger(LoggingLoader.class);

    private SystemParametersPort parameterPort;

    public LoggingLoader(SystemParametersPort parameterPort) {
        this.parameterPort = parameterPort;
    }

    public void changeLogLevel(String loggerName, String logLevelId) {
        try {
            Level logLevel = Level.toLevel(logLevelId, Level.INFO);

            parameterPort.setLogLevel(logLevel);

            this.reloadLogger(loggerName, logLevel);
        } catch (Exception e) {
            logger.error("Error while reloading Logger configuration", e);
        }
    }

    public void reloadLogger(String loggerName, String logLevelId) {
        Level logLevel = Level.toLevel(logLevelId, Level.INFO);

        reloadLogger(loggerName, logLevel);
    }

    public void reloadLogger(String loggerName, Level logLevel) {
        try {
            LoggerContext context = (LoggerContext) LoggerFactory.getILoggerFactory();
            ch.qos.logback.classic.Logger logbackLogger = (ch.qos.logback.classic.Logger) context.getLogger(loggerName);

            logbackLogger.setLevel(logLevel);

            listActiveLogLevels();
        } catch (Exception e) {
            logger.error("Error while reloading Logger configuration", e);
        }
    }

    private static void listActiveLogLevels() {
        logger.trace("Logger TRACE mode actif");
        logger.debug("Logger DEBUG mode actif");
        logger.info("Logger INFO mode actif");
        logger.warn("Logger WARN mode actif");
        logger.error("Logger ERROR mode actif (toujours actif)");
    }
}
