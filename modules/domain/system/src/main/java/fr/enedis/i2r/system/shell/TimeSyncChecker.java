package fr.enedis.i2r.system.shell;

import static fr.enedis.i2r.system.shell.ShellCommand.CHRONY_TRACKING;

import java.io.BufferedReader;
import java.io.IOException;
import java.time.Duration;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.system.ports.ShellExecutorPort;

public class TimeSyncChecker {

    private static final Logger logger = LoggerFactory.getLogger(TimeSyncChecker.class);

    private final ShellExecutorPort se;
    private final Duration threshold;

    public TimeSyncChecker(ShellExecutorPort se, Duration threshold) {
        this.se = se;
        this.threshold = threshold;
    }

    public boolean isTimeSynched() {
        boolean isSync = false;
        try (BufferedReader reader = se.execute(CHRONY_TRACKING)) {
            String line;
            while ((line = reader.readLine()) != null) {
                if (line.startsWith("Last offset")) {
                    int off = Integer.parseInt(line.split(":")[1].split("\\.")[0].trim());
                    isSync = Math.abs(off) < threshold.toSeconds();
                    if (!isSync) {
                        logger.warn("Time is not synched. {}", line);
                    }
                } else if (line.startsWith("Leap status")) {
                    if (line.contains("Not synchronised")) {
                        isSync = false;
                    }
                }
            }
        } catch (IOException e) {
            logger.error(e.getMessage());
        }
        return isSync;
    }
}
