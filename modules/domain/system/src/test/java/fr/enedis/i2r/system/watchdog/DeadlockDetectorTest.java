package fr.enedis.i2r.system.watchdog;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.lang.management.ThreadInfo;
import java.lang.management.ThreadMXBean;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.mockito.ArgumentCaptor;

class DeadlockDetectorTest {

    @Test
    void si_deadlock_la_sortie_en_erreur_est_declenche() {
        ThreadMXBean mockThreadBean = mock(ThreadMXBean.class);
        ThreadInfo mockThreadInfo1 = mock(ThreadInfo.class);
        ThreadInfo mockThreadInfo2 = mock(ThreadInfo.class);

        long[] deadlockedThreadIds = {1L, 2L};
        when(mockThreadBean.findDeadlockedThreads()).thenReturn(deadlockedThreadIds);
        when(mockThreadBean.getThreadInfo(deadlockedThreadIds)).thenReturn(new ThreadInfo[]{mockThreadInfo1, mockThreadInfo2});

        DeadlockHandler handler = mock(DeadlockHandler.class);

        DeadlockDetector detector = new DeadlockDetector(mockThreadBean, handler);

        detector.checkAndHandleDeadlocks();

        ArgumentCaptor<ThreadInfo[]> captor = ArgumentCaptor.forClass(ThreadInfo[].class);
        verify(handler, times(1)).handle(captor.capture());
        ThreadInfo[] passedInfos = captor.getValue();
        assertNotNull(passedInfos);
        assertEquals(2, passedInfos.length);
        assertSame(mockThreadInfo1, passedInfos[0]);
        assertSame(mockThreadInfo2, passedInfos[1]);
    }

    @Test
    void sans_deadlock_la_sortie_en_erreur_n_est_pas_declenche() {
        ThreadMXBean mockThreadBean = mock(ThreadMXBean.class);
        when(mockThreadBean.findDeadlockedThreads()).thenReturn(null);

        DeadlockHandler handler = mock(DeadlockHandler.class);

        DeadlockDetector detector = new DeadlockDetector(mockThreadBean, handler);

        detector.checkAndHandleDeadlocks();

        verify(handler, never()).handle(any());
    }

    @Test
    @Timeout(10)
    void plusieurs_appels_consecutifs_sans_deadlock_ne_declenchent_pas_de_sortie_en_erreur() {
        ThreadMXBean mockThreadBean = mock(ThreadMXBean.class);
        when(mockThreadBean.findDeadlockedThreads()).thenReturn(null);

        DeadlockHandler handler = mock(DeadlockHandler.class);

        DeadlockDetector detector = new DeadlockDetector(mockThreadBean, handler);

        for (int i = 0; i < 100; i++) {
            detector.checkAndHandleDeadlocks();
        }
        verify(handler, never()).handle(any());
    }

    @Test
    @Timeout(15)
    void la_detection_des_deadlocks_est_rapide() {
        ThreadMXBean mockThreadBean = mock(ThreadMXBean.class);

        long[] deadlockedThreadIds = {5L, 6L};
        ThreadInfo mockThreadInfo1 = mock(ThreadInfo.class);
        ThreadInfo mockThreadInfo2 = mock(ThreadInfo.class);

        when(mockThreadBean.findDeadlockedThreads()).thenReturn(deadlockedThreadIds);
        when(mockThreadBean.getThreadInfo(deadlockedThreadIds)).thenReturn(new ThreadInfo[]{mockThreadInfo1, mockThreadInfo2});

        DeadlockHandler handler = mock(DeadlockHandler.class);

        DeadlockDetector detector = new DeadlockDetector(mockThreadBean, handler);

        long startTime = System.currentTimeMillis();
        detector.checkAndHandleDeadlocks();
        long detectionTime = System.currentTimeMillis() - startTime;

        verify(handler, times(1)).handle(any(ThreadInfo[].class));
        assertTrue(detectionTime < 500, "Detection should be fast (< 0.5s), was: " + detectionTime + "ms");
    }
}
