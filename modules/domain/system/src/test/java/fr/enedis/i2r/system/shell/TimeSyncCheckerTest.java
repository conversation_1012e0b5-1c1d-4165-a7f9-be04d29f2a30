package fr.enedis.i2r.system.shell;

import static fr.enedis.i2r.system.shell.ShellCommand.CHRONY_TRACKING;
import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.io.BufferedReader;
import java.io.StringReader;
import java.time.Duration;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import fr.enedis.i2r.system.ports.ShellExecutorPort;

class TimeSyncCheckerTest {

    public static final Duration THRESHOLD_TIME_DESYNCHRONISATION = Duration.ofSeconds(1);
    TimeSyncChecker checker;
    ShellExecutorPort shell;

    @BeforeEach
    public void setup() {
        shell = mock(ShellExecutorPort.class);
        checker = new TimeSyncChecker(shell, THRESHOLD_TIME_DESYNCHRONISATION);
    }

    @Test
    void un_offset_positif_en_dessous_de_la_limite_retourne_true() {
        String text = """
                Last offset     : 0.000123456 seconds
                Leap status     : Normal
                """.stripIndent();
        BufferedReader reader = new BufferedReader(new StringReader(text));
        when(shell.execute(CHRONY_TRACKING)).thenReturn(reader);
        assertThat(checker.isTimeSynched()).isTrue();
    }

    @Test
    void un_offset_negatif_au_dessus_de_la_limite_retourne_false() {
        String text = """
                Last offset     : -2.000004740 seconds
                Leap status     : Normal
                """.stripIndent();
        BufferedReader reader = new BufferedReader(new StringReader(text));
        when(shell.execute(CHRONY_TRACKING)).thenReturn(reader);
        assertThat(checker.isTimeSynched()).isFalse();
    }

    @Test
    void le_statu_Not_synchronised_retourne_false() {
        String text = """
                Last offset     : +0.000000000 seconds
                Leap status     : Not synchronised
                """.stripIndent();
        BufferedReader reader = new BufferedReader(new StringReader(text));
        when(shell.execute(CHRONY_TRACKING)).thenReturn(reader);
        assertThat(checker.isTimeSynched()).isFalse();
    }
}
