package fr.enedis.i2r.system;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.reset;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import fr.enedis.i2r.system.leds.LedStatus;
import fr.enedis.i2r.system.leds.SignalLevel;
import fr.enedis.i2r.system.ports.LedPort;

public class SignalLevelTest {

    private LedPort mockLedPort;

    @BeforeEach
    void setup() {
        mockLedPort = mock(LedPort.class);
    }

    @Test
    void retourner_niveau_pour_rssi_dans_limites_good() {
        int rssi = 15;
        SignalLevel signalLevel = SignalLevel.fromRSSI(rssi);
        assertThat(signalLevel).isEqualTo(SignalLevel.GOOD);
    }

    @Test
    void retourner_led_status_pour_good_signal() {
        SignalLevel signalLevel = SignalLevel.GOOD;
        LedStatus ledStatus = signalLevel.toLedStatus();
        assertThat(ledStatus).isEqualTo(LedStatus.BLINK_GREEN);
    }

    @Test
    void retourner_no_signal_pour_rssi_hors_limites() {
        int rssi = -10;
        SignalLevel signalLevel = SignalLevel.fromRSSI(rssi);
        assertThat(signalLevel).isEqualTo(SignalLevel.NO_SIGNAL);
    }

    @Test
    void verifier_que_led_s_allume_pour_signal_excellent() {
        SignalLevel signalLevel = SignalLevel.EXCELLENT;
        LedStatus ledStatus = signalLevel.toLedStatus();
        assertThat(ledStatus).isEqualTo(LedStatus.ON_GREEN);
    }

    @Test
    void verifier_aucune_led_pour_no_signal() {
        SignalLevel signalLevel = SignalLevel.NO_SIGNAL;
        LedStatus ledStatus = signalLevel.toLedStatus();
        assertThat(ledStatus).isEqualTo(LedStatus.OFF);
    }

    @Test
    void verifier_comportement_led_pour_signal_variable() {
        int[] rssis = { 99, 0, 15, 31 };
        SignalLevel[] expectedLevels = {
                SignalLevel.NO_SIGNAL, SignalLevel.VERY_POOR, SignalLevel.GOOD, SignalLevel.EXCELLENT
        };

        for (int i = 0; i < rssis.length; i++) {
            SignalLevel signalLevel = SignalLevel.fromRSSI(rssis[i]);
            assertThat(signalLevel).isEqualTo(expectedLevels[i]);
        }
    }

    @AfterEach
    void teardown() {
        reset(mockLedPort);
    }
}
