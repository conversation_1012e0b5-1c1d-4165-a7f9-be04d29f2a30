package fr.enedis.i2r.system;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import fr.enedis.i2r.system.leds.LedName;
import fr.enedis.i2r.system.leds.LedStatus;
import fr.enedis.i2r.system.ports.LedPort;
import fr.enedis.i2r.system.ports.SystemdPort;
import fr.enedis.i2r.system.watchdog.ThreadWatchdog;

public class RslLedTest {

    private SystemConfiguration systemConfiguration;
    private LedPort ledPort;
    private SystemdPort systemdPort;

    @BeforeEach
    void setup() {
        systemConfiguration = mock(SystemConfiguration.class);
        ledPort = mock(LedPort.class);
        systemdPort = mock(SystemdPort.class);
    }

    @Test
    void l_etat_de_la_led_rsl_est_relaye_au_hal() {
        var threadWatchdog = mock(ThreadWatchdog.class);
        var system = new SystemModule(systemConfiguration, ledPort, systemdPort, threadWatchdog);

        system.setRslLed(LedStatus.BLINK_YELLOW);

        verify(ledPort).setLedStatus(LedName.RSL, LedStatus.BLINK_YELLOW);
    }
}
