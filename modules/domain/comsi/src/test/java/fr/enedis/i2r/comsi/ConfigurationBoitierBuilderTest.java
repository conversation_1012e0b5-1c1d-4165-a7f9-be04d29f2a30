package fr.enedis.i2r.comsi;

import static org.assertj.core.api.Assertions.assertThat;

import java.time.Duration;
import java.time.Instant;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class ConfigurationBoitierBuilderTest {

    private static final String HASH_EXEMPLE = "abc123";
    private CustomComSiConfiguration customConfig;

    @BeforeEach
    void initialiser_configuration() {
        customConfig = new CustomComSiConfiguration();
    }

    @Test
    void construit_une_configuration_boitier_valide() {
        var now = Instant.now();
        var configuration = ConfigurationBoitier.from(customConfig.build(),
                now,
                HASH_EXEMPLE, "ADS", "IDMS", "ICCID");

        assertThat(configuration).isNotNull();

        assertThat(configuration.nbBitsMasqueIpv4()).isEqualTo(24);
        assertThat(configuration.pingRetryLimit()).isEqualTo(3);
        assertThat(configuration.pingTimeout()).isEqualTo(Duration.ofSeconds(5));

        assertThat(configuration.configurationHash()).isEqualTo(HASH_EXEMPLE);
    }

}
