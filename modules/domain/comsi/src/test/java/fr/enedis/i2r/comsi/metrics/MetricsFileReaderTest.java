package fr.enedis.i2r.comsi.metrics;

import org.junit.jupiter.api.Test;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class MetricsFileReaderTest {

    @Test
    void le_retriever_est_capable_de_recuperer_le_fichier_de_metrics() throws MetricsException {
        var path = getResourcePath("fr/enedis/i2r/comsi/metrics/small_metrics.jsonl");
        var retriever = new MetricsFileReader(path);
        List<RawJsonMetric> result = retriever.getMetrics();
        assertThat(result).isNotEmpty();
        assertThat(result.size()).isEqualTo(3);
    }

    @Test
    void une_erreur_de_formatage_dans_la_source_ne_bloque_pas_le_process() throws MetricsException {
        var path = getResourcePath("fr/enedis/i2r/comsi/metrics/with_error.jsonl");
        var retriever = new MetricsFileReader(path);
        List<RawJsonMetric> result = retriever.getMetrics();
        assertThat(result).isNotEmpty();
        assertThat(result.size()).isEqualTo(2);
    }

    private String getResourcePath(String file) {
        ClassLoader classLoader = getClass().getClassLoader();
        var url = classLoader.getResource(file);
        assert url != null;
        return url.getPath();
    }
}
