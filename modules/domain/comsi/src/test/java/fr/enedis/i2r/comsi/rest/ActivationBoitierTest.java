package fr.enedis.i2r.comsi.rest;

import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import fr.enedis.i2r.comsi.errors.rest.InvalidRequestBodyException;
import fr.enedis.i2r.comsi.errors.rest.InvalidRequestHeadersException;
import fr.enedis.i2r.comsi.ports.BoardManagerPort;
import fr.enedis.i2r.comsi.ports.ComsiParametersPort;
import fr.enedis.i2r.comsi.rest.controllers.StatutBoitierController;
import fr.enedis.i2r.comsi.rest.requests.OrdreActivationBoitier;
import fr.enedis.i2r.comsi.status.BipStatus;
import fr.enedis.i2r.comsi.status.BipStatusManager;
import fr.enedis.i2r.system.ports.SystemModulePort;
import io.javalin.http.Context;

public class ActivationBoitierTest {

    private static final String DUMMY_IDMS = "dummy_idms_value";

    ComsiParametersPort parametersPort;
    BoardManagerPort boardManagerPort;
    SystemModulePort systemModulePort;
    Context ctx;

    @BeforeEach
    void setup() {
        parametersPort = mock(ComsiParametersPort.class);
        ctx = mock(Context.class);
        boardManagerPort = mock(BoardManagerPort.class);
        systemModulePort = mock(SystemModulePort.class);
    }

    @Test
    void le_boitier_passe_a_l_etat_stable_lorsqu_il_en_recoit_la_requete() throws Throwable {
        var data = new OrdreActivationBoitier();
        data.state = 2;
        when(ctx.bodyAsClass(any())).thenReturn(data);
        when(ctx.header("x-idms")).thenReturn(DUMMY_IDMS);
        when(ctx.header("X-ERDF-HASH")).thenReturn("VERITABLEHASH");

        when(parametersPort.getConfigurationHash()).thenReturn("VERITABLEHASH");

        var controller = new StatutBoitierController(new BipStatusManager(parametersPort, DUMMY_IDMS, systemModulePort));
        controller.changeStatus(ctx);

        verify(parametersPort, times(1)).updateBipStatus(BipStatus.STABLE);
    }

    @Test
    void on_ne_peut_pas_changer_le_statut_du_boitier_avec_des_valeurs_incoherentes() {
        var data = new OrdreActivationBoitier();
        data.state = 3;
        when(parametersPort.getConfigurationHash()).thenReturn("VERITABLEHASH");

        when(ctx.bodyAsClass(any())).thenReturn(data);
        when(ctx.header("x-idms")).thenReturn(DUMMY_IDMS);
        when(ctx.header("X-ERDF-HASH")).thenReturn("VERITABLEHASH");

        var controller = new StatutBoitierController(new BipStatusManager(parametersPort, DUMMY_IDMS, systemModulePort));
        assertThatExceptionOfType(InvalidRequestBodyException.class)
                .isThrownBy(() -> controller.changeStatus(ctx));

        verify(parametersPort, never()).updateBipStatus(BipStatus.STABLE);
    }

    @Test
    void le_hash_de_parametres_en_entete_doit_correspondre_a_celui_du_boitier() throws InvalidRequestHeadersException, InvalidRequestBodyException {
        var data = new OrdreActivationBoitier();
        data.state = 2;
        when(parametersPort.getConfigurationHash()).thenReturn("VERITABLEHASH");

        when(ctx.bodyAsClass(any())).thenReturn(data);
        when(ctx.header("x-idms")).thenReturn(DUMMY_IDMS);
        when(ctx.header("X-ERDF-HASH")).thenReturn("VERITABLEHASH");

        var controller = new StatutBoitierController(new BipStatusManager(parametersPort, DUMMY_IDMS, systemModulePort));
        controller.changeStatus(ctx);
        verify(parametersPort, times(1)).updateBipStatus(BipStatus.STABLE);
    }

    @Test
    void l_idms_de_parametres_en_entete_doit_correspondre_a_celui_du_boitier() {
        var data = new OrdreActivationBoitier();
        data.state = 2;
        when(parametersPort.getConfigurationHash()).thenReturn("VERITABLEHASH");

        when(ctx.bodyAsClass(any())).thenReturn(data);
        when(ctx.header("x-idms")).thenReturn(DUMMY_IDMS);
        when(ctx.header("X-ERDF-HASH")).thenReturn("WRONGHASH");

        var controller = new StatutBoitierController(new BipStatusManager(parametersPort, DUMMY_IDMS, systemModulePort));

        assertThatExceptionOfType(InvalidRequestHeadersException.class).isThrownBy(
                () -> controller.changeStatus(ctx));
        verify(parametersPort, never()).updateBipStatus(BipStatus.STABLE);
    }

}
