package fr.enedis.i2r.comsi;

import java.time.Duration;
import java.time.Instant;

public record ConfigurationBoitier(
    LogLevel logLevel,
    Instant lastValidityCheck,
    Integer nbBitsMasqueIpv4,
    Integer pingRetryLimit,
    IpAddress primaryDatacenterPingAddress,
    IpAddress secondaryDatacenterPingAddress,
    Duration pingTimeout,
    Integer resetModemLimit,
    String configurationHash,
    String ads,
    String idms,
    String iccid,
    int state) {

    public static ConfigurationBoitier from(ComSiConfiguration configuration,
                                            Instant lastValidityCheck,
                                            String configurationHash,
                                            String ads,
                                            String idms,
                                            String iccid) {
        return new ConfigurationBoitier(
            LogLevel.INFO, // TODO : parse from system
            lastValidityCheck,
            configuration.netmaskSize(),
            configuration.pingRetryLimit(),
            configuration.parseDatacenterConfiguration().primaryIp(),
            configuration.parseDatacenterConfiguration().secondaryIp(),
            configuration.pingTimeout(),
            configuration.modemResetsLimitBeforeAo3Reboot(),
            configurationHash,
            ads,
            idms,
            iccid,
            configuration.bipStatus().statusCode);
    }
}
