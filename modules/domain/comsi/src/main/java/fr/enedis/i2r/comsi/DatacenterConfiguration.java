package fr.enedis.i2r.comsi;


public record DatacenterConfiguration(
        IpAddress primaryIp,
        IpAddress secondaryIp) {

    public static DatacenterConfiguration from(ComSiConfiguration conf) {
        return switch (conf.primaryDatacenter()) {
            case NOE -> new DatacenterConfiguration(conf.noeIpv4(), conf.pacyIpv4());
            case PACY -> new DatacenterConfiguration(conf.pacyIpv4(), conf.noeIpv4());
        };
    }
}
