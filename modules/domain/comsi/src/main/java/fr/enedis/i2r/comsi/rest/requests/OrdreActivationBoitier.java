package fr.enedis.i2r.comsi.rest.requests;

import com.fasterxml.jackson.annotation.JsonProperty;

public class OrdreActivationBoitier {

        @JsonProperty("name")
        public String name;

        @JsonProperty("class")
        public String className;

        @JsonProperty("state")
        public int state;

        // Value of the header "X-ERDF-API-VERSION"
        public String erdfApiVersion;

        // Value of the header "x-idms"
        public String idms;

        // Value of the header "X-ERDF-HASH"
        public String erdfHash;
}
