package fr.enedis.i2r.comsi.ports.si;

import fr.enedis.i2r.comsi.errors.RequestToSiException;
import fr.enedis.i2r.comsi.status.BipStatus;

/**
 * Le but de cet abstraction est que ComSI puisse notifier l'infra
 * comme elle le souhaite lors de la mise à jour d'un paramètre
 *
 * Je sais que vous pensez qu'une méthode par paramètre, c'est long et lourd
 * Déjà, "titre", ensuite : c'est important que le SI côté infra puisse séparer ces cas.
 * Pour plusieurs raisons
 *
 * - à l'heure actuelle, on ne sait pas factoriser les envois de conf partielle au SI.
 *   - A chaque fois qu'un paramètre s'envoie, on construit l'objet au cas par cas puisqu'on
 *     doit s'adapter au contrat d'interface de l'AO2bis
 *   - Si on fait une grosse méthode "notifyParameterChange", on fera un switch case côté infra
 *     de toutes manières
 * - avoir une autre interface que "SiClientPort" permet de mieux ranger les méthodes,
 *   sinon SiClientHttpAdapter va faire 500 lignes à la fin (et si vous voulez vraiment,
 *   une classe peut implémenter plusieurs interfaces)
 */
public interface SiConfigurationNotifierPort {
    public void notifyStateChange(BipStatus newStatus) throws RequestToSiException;
}
