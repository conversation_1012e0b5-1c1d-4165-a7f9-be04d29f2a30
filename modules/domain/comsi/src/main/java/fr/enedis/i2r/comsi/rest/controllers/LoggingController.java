package fr.enedis.i2r.comsi.rest.controllers;

import static io.javalin.http.HttpStatus.OK;

import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.comsi.LogLevel;
import fr.enedis.i2r.comsi.errors.rest.InvalidRequestBodyException;
import fr.enedis.i2r.comsi.rest.RestEndpoints;
import fr.enedis.i2r.system.LoggingLoader;
import io.javalin.http.Context;
import io.javalin.openapi.HttpMethod;
import io.javalin.openapi.OpenApi;
import io.javalin.openapi.OpenApiContent;
import io.javalin.openapi.OpenApiParam;
import io.javalin.openapi.OpenApiResponse;

public class LoggingController {

    private static final Logger logger = LoggerFactory.getLogger(LoggingController.class);
    private static final String LOGGER_NAME = "ROOT";

    private LoggingLoader loggingLoader;

    public LoggingController(LoggingLoader loggingLoader) {
        this.loggingLoader = loggingLoader;
    }

    @OpenApi(summary = "Permet de changer le niveau de log applicatif de i2R",
        operationId = "changeLogLevel",
        path = RestEndpoints.API_ROOT + RestEndpoints.LOG_APP,
        methods = HttpMethod.POST,
        tags = { "Logs" },
        queryParams = {
            @OpenApiParam(name = "logLevel", type = LogLevel.class, required = true, description = "Niveau de log souhaité")
        },
        responses = {
            @OpenApiResponse(status = "200", description = "Niveau de log modifié", content = {@OpenApiContent(from = String.class) }),
            @OpenApiResponse(status = "400", description = "Paramètre d'entrée manquant", content = {@OpenApiContent(from = String.class) }),
            @OpenApiResponse(status = "500", description = "Erreur lors de l'exécution", content = {@OpenApiContent(from = String.class) })
        })
    public void changeLogLevel(Context ctx) throws InvalidRequestBodyException {
        String logLevel = Optional.ofNullable(ctx.queryParam("logLevel"))
            .orElseThrow(() -> new InvalidRequestBodyException("niveau de log manquant"));

        this.loggingLoader.changeLogLevel(LOGGER_NAME, logLevel);

        String msg = "Le niveau de log i2R a bien été modifié à : " + logLevel;
        logger.info(msg);
        ctx.status(OK).result(msg);
    }
}
