package fr.enedis.i2r.comsi.metrics;

import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;

import java.io.BufferedReader;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MetricsFileReader {

    private static final Logger logger = LoggerFactory.getLogger(MetricsFileReader.class);

    private final String path;
    private final Gson gson;

    public MetricsFileReader(String path) {
        this.path = path;
        this.gson = new Gson();
    }

    public List<RawJsonMetric> getMetrics() throws MetricsException {
        List<RawJsonMetric> rawJsonMetrics = new ArrayList<>();
        try (BufferedReader reader = Files.newBufferedReader(Path.of(this.path))) {
            String line;
            while ((line = reader.readLine()) != null) {
                try {
                    RawJsonMetric m = gson.fromJson(line, RawJsonMetric.class);
                    rawJsonMetrics.add(m);
                } catch (JsonSyntaxException e) {
                    logger.error("Erreur de lecture d'une ligne de {} : {}", path, line, e);
                }
            }
        } catch (IOException e) {
            throw new MetricsException(this.path, e);
        }
        return rawJsonMetrics;
    }
}
