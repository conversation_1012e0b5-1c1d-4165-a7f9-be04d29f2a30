package fr.enedis.i2r.comsi.errors.rest;

import io.javalin.http.HttpStatus;

public class InvalidRequestBodyException extends ComsiServerException {
    public InvalidRequestBodyException(String additionalContext) {
        super(String.format("contenu de la requête invalide: %s", additionalContext));
    }

    @Override
    public HttpStatus getStatusCode() {
        return HttpStatus.BAD_REQUEST;
    }

}
