package fr.enedis.i2r.comsi.params;

import static java.util.stream.Collectors.toMap;

import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Stream;

public enum BipParameter {
    BipState("i2r.bip.status", true);

    public final String parameterKey;
    public final boolean watched;

    private static final Map<String, BipParameter> BY_KEY = Stream.of(values())
            .collect(toMap(param -> param.parameterKey, Function.identity()));

    BipParameter(String parameterKey, boolean watched) {
        this.parameterKey = parameterKey;
        this.watched = watched;
    }

    public static Optional<BipParameter> fromParameterKey(String parameterKey) {
        return Optional.ofNullable(BY_KEY.get(parameterKey));
    }
}
