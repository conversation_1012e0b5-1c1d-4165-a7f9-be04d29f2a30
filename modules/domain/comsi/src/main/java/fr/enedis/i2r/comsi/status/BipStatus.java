package fr.enedis.i2r.comsi.status;

import static java.util.stream.Collectors.toMap;

import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Stream;

public enum BipStatus {
    INIT(1), STABLE(2);

    public final int statusCode;

    private static final Map<Integer, BipStatus> BY_CODE = Stream.of(values())
            .collect(toMap(status -> status.statusCode, Function.identity()));

    BipStatus(int statusCode) {
        this.statusCode = statusCode;
    }

    public static Optional<BipStatus> fromStatusCode(int statusCode) {
        return Optional.ofNullable(BY_CODE.get(statusCode));
    }
}
