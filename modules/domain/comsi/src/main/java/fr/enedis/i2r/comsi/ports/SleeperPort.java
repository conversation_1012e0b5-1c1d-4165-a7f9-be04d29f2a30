package fr.enedis.i2r.comsi.ports;

import java.time.Duration;

public interface SleeperPort {
    /**
     * <PERSON><PERSON> méthode attend le temps donné en paramètre.
     *
     * Le but est d'abstraire le Thread.sleep afin de ne pas réellement attendre
     * lors des tests.
     *
     * @param timeToWait La durée à attendre
     */
    void sleep(Duration timeToWait) throws InterruptedException;
}
