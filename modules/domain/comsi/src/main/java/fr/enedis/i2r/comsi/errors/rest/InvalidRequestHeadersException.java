package fr.enedis.i2r.comsi.errors.rest;

import io.javalin.http.HttpStatus;

public class InvalidRequestHeadersException extends ComsiServerException {

    public InvalidRequestHeadersException(String additionalContext) {
        super(String.format("headers de la requête invalides: %s", additionalContext));
    }

    @Override
    public HttpStatus getStatusCode() {
        return HttpStatus.BAD_REQUEST;
    }
}
