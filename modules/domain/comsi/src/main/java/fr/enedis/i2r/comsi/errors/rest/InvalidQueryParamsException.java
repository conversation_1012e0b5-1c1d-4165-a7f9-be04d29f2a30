package fr.enedis.i2r.comsi.errors.rest;

import io.javalin.http.HttpStatus;

public class InvalidQueryParamsException  extends ComsiServerException {

    public InvalidQueryParamsException (String additionalContext) {
        super(String.format("Parametre(s) de la query invalide(s): %s", additionalContext));
    }

    @Override
    public HttpStatus getStatusCode() {
        return HttpStatus.BAD_REQUEST;
    }
}
