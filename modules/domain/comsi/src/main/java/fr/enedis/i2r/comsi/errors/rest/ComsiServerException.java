package fr.enedis.i2r.comsi.errors.rest;

import io.javalin.http.HttpStatus;

/**
 * Erreur utilisable par les handlers de ComSI afin de pouvoir être gérées
 * par Javalin, en associant le nom code de statut
 */
public abstract class ComsiServerException extends Exception {
    public ComsiServerException(String message) {
        super(message);
    }

    public abstract HttpStatus getStatusCode();

}
