package fr.enedis.i2r.comsi.status;

import fr.enedis.i2r.comsi.errors.rest.InvalidRequestHeadersException;
import fr.enedis.i2r.comsi.ports.ComsiParametersPort;
import fr.enedis.i2r.system.ports.SystemModulePort;

public class BipStatusManager {
    private ComsiParametersPort parametersPort;
    private SystemModulePort systemModule;
    private String idms;

    public BipStatusManager(ComsiParametersPort parametersPort, String idms, SystemModulePort system) {
        this.parametersPort = parametersPort;
        this.systemModule = system;
        this.idms = idms;
    }

    public void changeBipStatus(BipStatus newStatus, String targetedIdms, String targetedConfigurationHash)
            throws InvalidRequestHeadersException {

        if (!targetedIdms.equalsIgnoreCase(this.idms)) {
            throw new InvalidRequestHeadersException(
                    String.format("l'idms de la configuration ciblé (%s) ne correspond pas à celui du boitier (%s)",
                            targetedIdms, this.idms));
        }

        String bipConfigurationHash = parametersPort.getConfigurationHash();
        if (!bipConfigurationHash.equalsIgnoreCase(targetedConfigurationHash)) {
            throw new InvalidRequestHeadersException(
                    String.format("le hash de la configuration ciblé (%s) ne correspond pas à celui du boitier (%s)",
                            targetedConfigurationHash, bipConfigurationHash));
        }

        parametersPort.updateBipStatus(newStatus);
        systemModule.activateSecondaryServices();
        systemModule.startSecondaryServices();
    }

}
