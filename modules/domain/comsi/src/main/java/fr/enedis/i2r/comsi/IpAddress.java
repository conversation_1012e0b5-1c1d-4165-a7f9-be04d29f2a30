package fr.enedis.i2r.comsi;

public record IpAddress(String ip) {

    public int getNetmask(int nbBitInNetmask) {
        int maskValue = -1 << (32 - nbBitInNetmask);
        String[] ipParts = ip.split("\\.");
        int sum = 0;
        for (int i = 0; i < ipParts.length; i++) {
            sum += Integer.parseInt(ipParts[i]) << (24 - 8 * i);
        }
        return sum & maskValue;
    }

    public String toString() {
        return ip;
    }
}
