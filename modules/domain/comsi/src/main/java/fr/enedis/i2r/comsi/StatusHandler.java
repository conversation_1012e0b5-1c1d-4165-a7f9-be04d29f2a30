package fr.enedis.i2r.comsi;

import fr.enedis.i2r.comsi.ports.ComsiParametersPort;
import fr.enedis.i2r.comsi.status.BipStatus;

public class StatusHandler {
    private ComsiParametersPort parametersPort;

    public StatusHandler(ComsiParametersPort parametersPort) {
        this.parametersPort = parametersPort;
    }

    public void updateStatus(BipStatus newStatus) {
        this.parametersPort.updateBipStatus(newStatus);
    }

}
