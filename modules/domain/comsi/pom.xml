<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns="http://maven.apache.org/POM/4.0.0"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>fr.enedis.i2r</groupId>
        <artifactId>domain</artifactId>
        <version>1.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>comsi</artifactId>

    <dependencies>
        <dependency>
            <groupId>fr.enedis.i2r</groupId>
            <artifactId>system</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- REST API -->
        <dependency>
            <groupId>io.javalin</groupId>
            <artifactId>javalin</artifactId>
            <version>${javalin.version}</version>
        </dependency>
        <dependency>
            <groupId>io.javalin.community.openapi</groupId>
            <artifactId>openapi-annotation-processor</artifactId>
            <version>${javalin.version}</version>
        </dependency>
        <dependency>
            <groupId>io.javalin.community.openapi</groupId>
            <artifactId>javalin-openapi-plugin</artifactId>
            <version>${javalin.version}</version>
        </dependency>
        <dependency>
            <groupId>io.javalin.community.ssl</groupId>
            <artifactId>ssl-plugin</artifactId>
            <version>${javalin.version}</version>
        </dependency>
        <dependency>
            <groupId>io.javalin.community.openapi</groupId>
            <artifactId>javalin-swagger-plugin</artifactId>
            <version>${javalin.version}</version>
        </dependency>
        <dependency>
            <groupId>io.swagger.parser.v3</groupId>
            <artifactId>swagger-parser</artifactId>
            <version>2.1.22</version>
        </dependency>

        <!-- JSON parsing -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.18.2</version>
        </dependency>
    </dependencies>

</project>
