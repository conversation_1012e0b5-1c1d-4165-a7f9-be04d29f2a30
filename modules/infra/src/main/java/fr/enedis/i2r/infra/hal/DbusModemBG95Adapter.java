package fr.enedis.i2r.infra.hal;

import org.freedesktop.dbus.exceptions.DBusException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.hal.ModemManager1;
import fr.enedis.i2r.comsi.ports.ModemManagerPort;

public class DbusModemBG95Adapter implements ModemManagerPort {

    private static final Logger logger = LoggerFactory.getLogger(DbusModemBG95Adapter.class);

    private static final String DBUS_MODEM_PATH = "/fr/enedis/HAL/ModemManager1";
    private static final String DBUS_MODEM_NAME = "fr.enedis.HAL.ModemManager1";

    private final ModemManager1 modemManager;

    public DbusModemBG95Adapter(ModemManager1 modemManager) {
        this.modemManager = modemManager;
    }

    public static DbusModemBG95Adapter connect() throws IllegalStateException {
        try {
            var modemManager = DbusConnectionManager.getInstance().getRemoteObject(
                    DBUS_MODEM_NAME,
                    DBUS_MODEM_PATH,
                    ModemManager1.class);

            return new DbusModemBG95Adapter(modemManager);
        } catch (DBusException e) {
            logger.error("Failed to initialize ModemManager instance", e);
            throw new IllegalStateException("Could not connect to DBus Modem Manager", e);
        }
    }

    @Override
    public String getIccid() throws Exception {
        try {
            return this.modemManager.getICCID();
        } catch (Exception e) {
            logger.error("échec de l'obtention du iccd", e);
            throw new Exception("requête du signal du modem");
        }
    }
}
