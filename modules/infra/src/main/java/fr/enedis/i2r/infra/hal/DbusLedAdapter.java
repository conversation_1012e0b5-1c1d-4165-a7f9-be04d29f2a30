package fr.enedis.i2r.infra.hal;

import org.freedesktop.dbus.exceptions.DBusException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.hal.LEDManager1;
import fr.enedis.i2r.system.leds.LedName;
import fr.enedis.i2r.system.leds.LedStatus;
import fr.enedis.i2r.system.ports.LedPort;

public class DbusLedAdapter implements LedPort {

    private static final Logger logger = LoggerFactory.getLogger(DbusLedAdapter.class);

    private LEDManager1 ledManager;

    public DbusLedAdapter() {
        try {
            this.ledManager = DbusConnectionManager.getInstance().getRemoteObject(
                    "fr.enedis.HAL.LEDManager1",
                    "/fr/enedis/HAL/LEDManager1",
                    LEDManager1.class);
        } catch (DBusException e) {
            logger.error("Error while instantiating DBusLedAdapter", e);
        }
    }

    public void setLedStatus(LedName led, LedStatus status) {
        switch (led) {
            case LedName.POWER:
                ledManager.setPowerLedStatus(status.getValue());
                break;
            case LedName.CPT:
                ledManager.setCLedStatus(status.getValue());
                break;
            case LedName.ETH1:
                ledManager.setEth1LedStatus(status.getValue());
                break;
            case LedName.ETH2:
                ledManager.setEth2LedStatus(status.getValue());
                break;
            case LedName.SI:
                ledManager.setSILedStatus(status.getValue());
                break;
            case LedName.RSL:
                ledManager.setRSLLedStatusAndColor(status.getValue());
                break;
        }
    }

    public LedStatus getLedStatus(LedName led) {
        LedStatus status = LedStatus.OFF;
        switch (led) {
            case LedName.POWER:
                status = LedStatus.fromByte(ledManager.getPowerLedStatus()).get();
                break;
            case LedName.CPT:
                status = LedStatus.fromByte(ledManager.getCLedStatus()).get();
                break;
            case LedName.ETH1:
                status = LedStatus.fromByte(ledManager.getEth1LedStatus()).get();
                break;
            case LedName.ETH2:
                status = LedStatus.fromByte(ledManager.getEth2LedStatus()).get();
                break;
            case LedName.SI:
                status = LedStatus.fromByte(ledManager.getSILedStatus()).get();
                break;
            case LedName.RSL:
                status = LedStatus.fromByte(ledManager.getRSLLedStatusAndColor()).get();
                break;
        }
        return status;
    }
}
