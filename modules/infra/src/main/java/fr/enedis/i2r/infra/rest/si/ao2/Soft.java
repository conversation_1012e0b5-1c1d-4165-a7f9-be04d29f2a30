package fr.enedis.i2r.infra.rest.si.ao2;

import java.util.List;

public class Soft extends Base {
    private List<SoftwareApplication> applications;
    private SoftwareComponent initrd;
    private SoftwareComponent kernel;
    private List<SoftwareComponent> rootfs;

    public Soft() {
        this.setClazz("Software");
        this.setName("soft");
    }

    public List<SoftwareApplication> getApplications() {
        return applications;
    }

    public void setApplications(List<SoftwareApplication> applications) {
        this.applications = applications;
    }

    public SoftwareComponent getInitrd() {
        return initrd;
    }

    public void setInitrd(SoftwareComponent initrd) {
        this.initrd = initrd;
    }

    public SoftwareComponent getKernel() {
        return kernel;
    }

    public void setKernel(SoftwareComponent kernel) {
        this.kernel = kernel;
    }

    public List<SoftwareComponent> getRootfs() {
        return rootfs;
    }

    public void setRootfs(List<SoftwareComponent> rootfs) {
        this.rootfs = rootfs;
    }
}
