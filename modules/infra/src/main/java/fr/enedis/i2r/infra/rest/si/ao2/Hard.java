package fr.enedis.i2r.infra.rest.si.ao2;

import java.util.List;

public class Hard extends Base {
    private int ledLteLow;
    private int ledLteMed;
    private int ledLteHigh;
    private String adsid;
    private Integer antenna;
    private String autotestResult;
    private int batteryLevel;
    private int cpuLevel;
    private String currentTime;
    private String dateResetBattery;
    private String firstUseDate;
    private int flashTotal;
    private String iccid;
    private String imei;
    private int ledGprsHigh;
    private int ledGprsLow;
    private int ledGprsMed;
    private int ledUmtsHigh;
    private int ledUmtsLow;
    private int ledUmtsMed;
    private String manufacturer;
    private int microCut;
    private List<Partition> partitions;
    private String productClass;
    private int ramLevel;
    private int ramTotal;
    private String serialNumber;
    private Integer supplyType;
    private int uartSpeed;
    private Integer upTime;

    public Hard() {
        this.clazz = "Hardware";
        this.name = "hard";
    }

    public Integer getLedLteLow() {
        return ledLteLow;
    }

    public void setLedLteLow(Integer ledLteLow) {
        this.ledLteLow = ledLteLow;
    }

    public Integer getLedLteMed() {
        return ledLteMed;
    }

    public void setLedLteMed(Integer ledLteMed) {
        this.ledLteMed = ledLteMed;
    }

    public Integer getLedLteHigh() {
        return ledLteHigh;
    }

    public void setLedLteHigh(Integer ledLteHigh) {
        this.ledLteHigh = ledLteHigh;
    }

    public String getAdsid() {
        return adsid;
    }

    public void setAdsid(String adsid) {
        this.adsid = adsid;
    }

    public Integer getAntenna() {
        return antenna;
    }

    public void setAntenna(Integer antenna) {
        this.antenna = antenna;
    }

    public String getAutotestResult() {
        return autotestResult;
    }

    public void setAutotestResult(String autotestResult) {
        this.autotestResult = autotestResult;
    }

    public int getBatteryLevel() {
        return batteryLevel;
    }

    public void setBatteryLevel(int batteryLevel) {
        this.batteryLevel = batteryLevel;
    }

    public int getCpuLevel() {
        return cpuLevel;
    }

    public void setCpuLevel(int cpuLevel) {
        this.cpuLevel = cpuLevel;
    }

    public String getCurrentTime() {
        return currentTime;
    }

    public void setCurrentTime(String currentTime) {
        this.currentTime = currentTime;
    }

    public String getDateResetBattery() {
        return dateResetBattery;
    }

    public void setDateResetBattery(String dateResetBattery) {
        this.dateResetBattery = dateResetBattery;
    }

    public String getFirstUseDate() {
        return firstUseDate;
    }

    public void setFirstUseDate(String firstUseDate) {
        this.firstUseDate = firstUseDate;
    }

    public int getFlashTotal() {
        return flashTotal;
    }

    public void setFlashTotal(int flashTotal) {
        this.flashTotal = flashTotal;
    }

    public String getIccid() {
        return iccid;
    }

    public void setIccid(String iccid) {
        this.iccid = iccid;
    }

    public String getImei() {
        return imei;
    }

    public void setImei(String imei) {
        this.imei = imei;
    }

    public int getLedGprsHigh() {
        return ledGprsHigh;
    }

    public void setLedGprsHigh(int ledGprsHigh) {
        this.ledGprsHigh = ledGprsHigh;
    }

    public int getLedGprsLow() {
        return ledGprsLow;
    }

    public void setLedGprsLow(int ledGprsLow) {
        this.ledGprsLow = ledGprsLow;
    }

    public int getLedGprsMed() {
        return ledGprsMed;
    }

    public void setLedGprsMed(int ledGprsMed) {
        this.ledGprsMed = ledGprsMed;
    }

    public int getLedUmtsHigh() {
        return ledUmtsHigh;
    }

    public void setLedUmtsHigh(int ledUmtsHigh) {
        this.ledUmtsHigh = ledUmtsHigh;
    }

    public int getLedUmtsLow() {
        return ledUmtsLow;
    }

    public void setLedUmtsLow(int ledUmtsLow) {
        this.ledUmtsLow = ledUmtsLow;
    }

    public int getLedUmtsMed() {
        return ledUmtsMed;
    }

    public void setLedUmtsMed(int ledUmtsMed) {
        this.ledUmtsMed = ledUmtsMed;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public int getMicroCut() {
        return microCut;
    }

    public void setMicroCut(int microCut) {
        this.microCut = microCut;
    }

    public List<Partition> getPartitions() {
        return partitions;
    }

    public void setPartitions(List<Partition> partitions) {
        this.partitions = partitions;
    }

    public String getProductClass() {
        return productClass;
    }

    public void setProductClass(String productClass) {
        this.productClass = productClass;
    }

    public int getRamLevel() {
        return ramLevel;
    }

    public void setRamLevel(int ramLevel) {
        this.ramLevel = ramLevel;
    }

    public int getRamTotal() {
        return ramTotal;
    }

    public void setRamTotal(int ramTotal) {
        this.ramTotal = ramTotal;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public Integer getSupplyType() {
        return supplyType;
    }

    public void setSupplyType(Integer supplyType) {
        this.supplyType = supplyType;
    }

    public int getUartSpeed() {
        return uartSpeed;
    }

    public void setUartSpeed(int uartSpeed) {
        this.uartSpeed = uartSpeed;
    }

    public Integer getUpTime() {
        return upTime;
    }

    public void setUpTime(Integer upTime) {
        this.upTime = upTime;
    }

}
