package fr.enedis.i2r.infra.rest.si.ao2;

import java.util.Map;

/**
 * TODO: Figure out to what this class template corresponds in the schema
 */
public class IpSec extends Base {
    private Map<String, Object> ipv4;
    private Map<String, Object> ipv6;

    public Map<String, Object> getIpv4() {
        return ipv4;
    }

    public void setIpv4(Map<String, Object> ipv4) {
        this.ipv4 = ipv4;
    }

    public Map<String, Object> getIpv6() {
        return ipv6;
    }

    public void setIpv6(Map<String, Object> ipv6) {
        this.ipv6 = ipv6;
    }
}
