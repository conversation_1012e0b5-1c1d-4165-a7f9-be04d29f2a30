package fr.enedis.i2r.infra.rest.si.ao2;

public class PPP extends Base {
    private int authentication;
    private Integer currentMRU;
    private boolean enable;
    private boolean ipcpEnable;
    private int maxMRU;
    private int maxMTU;

    public PPP() {
        this.setClazz("PPP");
        this.setName("ppp");
    }

    public int getAuthentication() {
        return authentication;
    }

    public void setAuthentication(int authentication) {
        this.authentication = authentication;
    }

    public Integer getCurrentMRU() {
        return currentMRU;
    }

    public void setCurrentMRU(Integer currentMRU) {
        this.currentMRU = currentMRU;
    }

    public boolean isEnable() {
        return enable;
    }

    public void setEnable(boolean enable) {
        this.enable = enable;
    }

    public boolean isIpcpEnable() {
        return ipcpEnable;
    }

    public void setIpcpEnable(boolean ipcpEnable) {
        this.ipcpEnable = ipcpEnable;
    }

    public int getMaxMRU() {
        return maxMRU;
    }

    public void setMaxMRU(int maxMRU) {
        this.maxMRU = maxMRU;
    }

    public int getMaxMTU() {
        return maxMTU;
    }

    public void setMaxMTU(int maxMTU) {
        this.maxMTU = maxMTU;
    }
}
