package fr.enedis.i2r.infra.hal;

import org.freedesktop.dbus.exceptions.DBusException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.hal.BoardManager1;
import fr.enedis.i2r.comsi.ports.BoardManagerPort;

public class DbusBoardManagerAdapter implements BoardManagerPort {

    private static final Logger logger = LoggerFactory.getLogger(DbusBoardManagerAdapter.class);

    private BoardManager1 boardManager;

    public DbusBoardManagerAdapter() {
        try {
            this.boardManager = DbusConnectionManager.getInstance().getRemoteObject(
                    "fr.enedis.HAL.BoardManager1",
                    "/fr/enedis/HAL/BoardManager1",
                    BoardManager1.class);
        } catch (DBusException e) {
            logger.error("Error while instantiating DBusLedAdapter", e);
        }
    }

    @Override
    public String getAds() {
        return this.boardManager.getADS();
    }

}
