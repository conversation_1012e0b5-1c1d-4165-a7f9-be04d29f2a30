package fr.enedis.i2r.infra.params;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.HashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import ch.qos.logback.classic.Level;
import fr.enedis.i2r.comsi.ComSiConfiguration;
import fr.enedis.i2r.comsi.params.BipParameter;
import fr.enedis.i2r.comsi.ports.ComsiParametersPort;
import fr.enedis.i2r.comsi.status.BipStatus;
import fr.enedis.i2r.infra.params.errors.InvalidParameterException;
import fr.enedis.i2r.infra.params.errors.RequiredParameterMissingException;
import fr.enedis.i2r.infra.params.providers.ComSiParameters;
import fr.enedis.i2r.infra.params.providers.SystemParameters;
import fr.enedis.i2r.system.SystemConfiguration;
import fr.enedis.i2r.system.ports.SystemParametersPort;

public class SqliteParamsProvider implements SystemParametersPort, ComsiParametersPort {

    private static final Logger logger = LoggerFactory.getLogger(SqliteParamsProvider.class);

    private final Connection connection;

    private Configuration configuration;
    private SqliteParamsHasher hasher;

    public SqliteParamsProvider() throws SQLException {
        this(Constants.DEFAULT_DATABASE_PATH);
    }

    public SqliteParamsProvider(String dbPath) throws SQLException {
        this.connection = DriverManager.getConnection("jdbc:sqlite:" + dbPath);
        ensureTableIsCreated();
        this.configuration = fetchConfiguration();
    }

    private void ensureTableIsCreated() throws SQLException {
        var sql = "";
        sql = String.format("""
                CREATE TABLE IF NOT EXISTS %s(
                    %s text not null primary key,
                    %s text not null default 0,
                    %s boolean
                )""",
            Constants.PARAMS_TABLE_NAME,
            Constants.PARAMS_LABEL_COLUMN_NAME,
            Constants.PARAMS_VALUE_COLUMN_NAME,
            Constants.PARAMS_UPDATED_COLUMN_NAME);

        try (var pstmt = this.connection.prepareStatement(sql)) {
            pstmt.executeUpdate();
        }

        // Triggers cannot use variables, so we have to fix the value in the query string
        sql = String.format("""
                CREATE TRIGGER IF NOT EXISTS set_updated_true
                AFTER UPDATE OF %s ON %s
                FOR EACH ROW
                WHEN OLD.%s = NEW.%s AND OLD.%s <> NEW.%s
                BEGIN
                    UPDATE %s
                    SET %s = 1
                    WHERE %s = NEW.%s;
                END;""",
            Constants.PARAMS_VALUE_COLUMN_NAME,
            Constants.PARAMS_TABLE_NAME,
            Constants.PARAMS_UPDATED_COLUMN_NAME,
            Constants.PARAMS_UPDATED_COLUMN_NAME,
            Constants.PARAMS_VALUE_COLUMN_NAME,
            Constants.PARAMS_VALUE_COLUMN_NAME,
            Constants.PARAMS_TABLE_NAME,
            Constants.PARAMS_UPDATED_COLUMN_NAME,
            Constants.PARAMS_LABEL_COLUMN_NAME,
            Constants.PARAMS_LABEL_COLUMN_NAME);

        try (var pstmt = this.connection.prepareStatement(sql)) {
            pstmt.executeUpdate();
        }

        // Triggers cannot use variables, so we have to fix the value in the query string
        sql = String.format("""
                CREATE TRIGGER IF NOT EXISTS set_updated_true_for_inserts
                AFTER INSERT ON %s
                FOR EACH ROW
                BEGIN
                    UPDATE %s
                    SET %s = 1
                    WHERE %s = NEW.%s;
                END;""",
            Constants.PARAMS_TABLE_NAME,
            Constants.PARAMS_TABLE_NAME,
            Constants.PARAMS_UPDATED_COLUMN_NAME,
            Constants.PARAMS_LABEL_COLUMN_NAME,
            Constants.PARAMS_LABEL_COLUMN_NAME);

        try (var pstmt = this.connection.prepareStatement(sql)) {
            pstmt.executeUpdate();
        }
    }

    private Configuration fetchConfiguration() throws SQLException {
        var sql = String.format("SELECT %s, %s FROM %s", Constants.PARAMS_LABEL_COLUMN_NAME,
                Constants.PARAMS_VALUE_COLUMN_NAME, Constants.PARAMS_TABLE_NAME);

        HashMap<String, String> parameters = new HashMap<>();

        try (var pstmt = this.connection.prepareStatement(sql);
                var rs = pstmt.executeQuery()) {

            while (rs.next()) {
                parameters.put(rs.getString(Constants.PARAMS_LABEL_COLUMN_NAME),
                        rs.getString(Constants.PARAMS_VALUE_COLUMN_NAME));
            }

            return new Configuration(parameters);
        } catch (SQLException e) {
            logger.error("Récupération des paramètres dans la base SQLite", e);
            throw e;
        }
    }

    public void upsertParameter(String key, String newValue) throws SQLException {
        var sql = "INSERT INTO parameters(param_name, param_value) VALUES(?, ?) ON CONFLICT(param_name) DO UPDATE SET param_value=excluded.param_value";
        var upsertStatement = this.connection.prepareStatement(sql);

        upsertStatement.setString(1, key);
        upsertStatement.setString(2, newValue);

        upsertStatement.executeUpdate();
    }

    @Override
    public String getConfigurationHash() {
        hasher = new SqliteParamsHasher(configuration);
        return hasher.generateTableHash();
    }

    @Override
    public SystemConfiguration getSystemConfiguration() {
        return SystemParameters.getSystemConfiguration(configuration);
    }

    public ComSiConfiguration getComSiConfiguration()
            throws RequiredParameterMissingException, InvalidParameterException {
        return ComSiParameters.getComSiConfiguration(configuration);
    }

    public void refreshConfiguration() throws SQLException {
        this.configuration = fetchConfiguration();
    }

    @Override
    public void updateBipStatus(BipStatus status) {
        try {
            this.upsertParameter(BipParameter.BipState.parameterKey, Integer.toString(status.statusCode));
            this.refreshConfiguration();
        } catch (SQLException sqle) {
            logger.error("erreur lors de la mise à jour du statut du bip en base de données", sqle);
        }
    }

    @Override
    public void setLogLevel(Level logLevel) {
        try {
            this.upsertParameter(SystemParameters.LOG_LEVEL.primaryKey(), logLevel.levelStr);
            this.refreshConfiguration();
        } catch (SQLException sqle) {
            logger.error("erreur lors de la mise à jour du niveau de logs", sqle);
        }
    }

    public HashMap<String, String> fetchAndResetUpdatedParameters() {
        logger.info("Fetching updated parameters from database");
        var sql = String.format("SELECT %s, %s FROM %s WHERE %s = ?",
                Constants.PARAMS_LABEL_COLUMN_NAME,
                Constants.PARAMS_VALUE_COLUMN_NAME,
                Constants.PARAMS_TABLE_NAME,
                Constants.PARAMS_UPDATED_COLUMN_NAME);

        HashMap<String, String> parameters = new HashMap<>();

        try {
            connection.setAutoCommit(false);

            try (var pstmt = this.connection.prepareStatement(sql)) {
                pstmt.setBoolean(1, true);

                try (var rs = pstmt.executeQuery()) {
                    while (rs.next()) {
                        parameters.put(rs.getString(Constants.PARAMS_LABEL_COLUMN_NAME),
                                rs.getString(Constants.PARAMS_VALUE_COLUMN_NAME));
                    }
                }
            }

            // Reset updated parameters in the same transaction
            this.resetUpdatedParametersInTransaction();

            connection.commit();

        } catch (SQLException e) {
            logger.error("Error while fetching updated parameters from database", e);
            try {
                connection.rollback();
            } catch (SQLException rollbackException) {
                logger.error("Error during rollback", rollbackException);
            }
        } finally {
            try {
                connection.setAutoCommit(true);
            } catch (SQLException e) {
                logger.error("Error restoring auto-commit", e);
            }
        }

        return parameters;
    }

    public void resetUpdatedParameters() {
        logger.info("Reseting updated parameters on database");
        try (var pstmt = this.connection.prepareStatement(
                String.format("UPDATE %s SET %s = ?", Constants.PARAMS_TABLE_NAME, Constants.PARAMS_UPDATED_COLUMN_NAME))) {
            pstmt.setBoolean(1, false);
            pstmt.executeUpdate();
        } catch (SQLException e) {
            logger.error("Error while reseting updated parameters on database", e);
        }
    }

    private void resetUpdatedParametersInTransaction() throws SQLException {
        logger.debug("Reseting updated parameters in transaction");
        try (var pstmt = this.connection.prepareStatement(
                String.format("UPDATE %s SET %s = ?", Constants.PARAMS_TABLE_NAME, Constants.PARAMS_UPDATED_COLUMN_NAME))) {
            pstmt.setBoolean(1, false);
            pstmt.executeUpdate();
        }
    }

}
