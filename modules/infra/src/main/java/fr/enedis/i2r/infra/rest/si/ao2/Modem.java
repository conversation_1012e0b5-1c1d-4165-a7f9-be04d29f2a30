package fr.enedis.i2r.infra.rest.si.ao2;

public class Modem extends Base {
    private Integer rsrp;
    private Integer rscp;
    private Integer rxLevel;
    private String techno;

    public Modem() {
        this.setClazz(("Modem"));
        this.setName("modem");
    }

    public Integer getRsrp() {
        return rsrp;
    }

    public void setRsrp(Integer rsrp) {
        this.rsrp = rsrp;
    }

    public Integer getRscp() {
        return rscp;
    }

    public void setRscp(Integer rscp) {
        this.rscp = rscp;
    }

    public Integer getRxLevel() {
        return rxLevel;
    }

    public void setRxLevel(Integer rxLevel) {
        this.rxLevel = rxLevel;
    }

    public String getTechno() {
        return techno;
    }

    public void setTechno(String techno) {
        this.techno = techno;
    }
}
