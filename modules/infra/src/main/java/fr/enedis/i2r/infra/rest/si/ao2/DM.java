package fr.enedis.i2r.infra.rest.si.ao2;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

/**
 * Ici on spécifie qu'on ne veut pas sérialiser les valeurs null
 */
@JsonInclude(Include.NON_NULL)
public class DM extends Base {
    private List<APN> apn;
    private Integer gzipLevel;
    private Integer httpTmo;
    private Integer linkType;
    private LogConfig log;
    private Integer nbBitsMasqueIPV4;
    private Integer nbBitsMasqueIPV6;
    private String pin;
    private Boolean pinEnabled;
    private Integer pingFreq;
    private List<String> pingIP1;
    private List<String> pingIP2;
    private Integer pingTmo1;
    private Integer pingTmo2;
    private Boolean reachable;
    private Integer resetTmo;
    private Integer resetWanTmo;
    private Integer retryFreq;
    private Integer state;
    private Integer system;
    private Integer tcpSynRetry;
    private String validDate;
    private String version;
    private String hash;

    public DM() {
        this.setName("dm");
        this.setClazz("DM");
        this.setFlow("/db/cfg/flows/cfg");
    }

    public List<APN> getApn() {
        return apn;
    }

    public void setApn(List<APN> apn) {
        this.apn = apn;
    }

    public Integer getGzipLevel() {
        return gzipLevel;
    }

    public void setGzipLevel(Integer gzipLevel) {
        this.gzipLevel = gzipLevel;
    }

    public Integer getHttpTmo() {
        return httpTmo;
    }

    public void setHttpTmo(Integer httpTmo) {
        this.httpTmo = httpTmo;
    }

    public Integer getLinkType() {
        return linkType;
    }

    public void setLinkType(Integer linkType) {
        this.linkType = linkType;
    }

    public LogConfig getLog() {
        return log;
    }

    public void setLog(LogConfig log) {
        this.log = log;
    }

    public Integer getNbBitsMasqueIPV4() {
        return nbBitsMasqueIPV4;
    }

    public void setNbBitsMasqueIPV4(Integer nbBitsMasqueIPV4) {
        this.nbBitsMasqueIPV4 = nbBitsMasqueIPV4;
    }

    public Integer getNbBitsMasqueIPV6() {
        return nbBitsMasqueIPV6;
    }

    public void setNbBitsMasqueIPV6(Integer nbBitsMasqueIPV6) {
        this.nbBitsMasqueIPV6 = nbBitsMasqueIPV6;
    }

    public String getPin() {
        return pin;
    }

    public void setPin(String pin) {
        this.pin = pin;
    }

    public Boolean isPinEnabled() {
        return pinEnabled;
    }

    public void setPinEnabled(Boolean pinEnabled) {
        this.pinEnabled = pinEnabled;
    }

    public Integer getPingFreq() {
        return pingFreq;
    }

    public void setPingFreq(Integer pingFreq) {
        this.pingFreq = pingFreq;
    }

    public List<String> getPingIP1() {
        return pingIP1;
    }

    public void setPingIP1(List<String> pingIP1) {
        this.pingIP1 = pingIP1;
    }

    public List<String> getPingIP2() {
        return pingIP2;
    }

    public void setPingIP2(List<String> pingIP2) {
        this.pingIP2 = pingIP2;
    }

    public Integer getPingTmo1() {
        return pingTmo1;
    }

    public void setPingTmo1(Integer pingTmo1) {
        this.pingTmo1 = pingTmo1;
    }

    public Integer getPingTmo2() {
        return pingTmo2;
    }

    public void setPingTmo2(Integer pingTmo2) {
        this.pingTmo2 = pingTmo2;
    }

    public Boolean isReachable() {
        return reachable;
    }

    public void setReachable(Boolean reachable) {
        this.reachable = reachable;
    }

    public Integer getResetTmo() {
        return resetTmo;
    }

    public void setResetTmo(Integer resetTmo) {
        this.resetTmo = resetTmo;
    }

    public Integer getResetWanTmo() {
        return resetWanTmo;
    }

    public void setResetWanTmo(Integer resetWanTmo) {
        this.resetWanTmo = resetWanTmo;
    }

    public Integer getRetryFreq() {
        return retryFreq;
    }

    public void setRetryFreq(Integer retryFreq) {
        this.retryFreq = retryFreq;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Integer getSystem() {
        return system;
    }

    public void setSystem(Integer system) {
        this.system = system;
    }

    public Integer getTcpSynRetry() {
        return tcpSynRetry;
    }

    public void setTcpSynRetry(Integer tcpSynRetry) {
        this.tcpSynRetry = tcpSynRetry;
    }

    public String getValidDate() {
        return validDate;
    }

    public void setValidDate(String validDate) {
        this.validDate = validDate;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getHash() {
        return hash;
    }

    public void setHash(String hash) {
        this.hash = hash;
    }
}
