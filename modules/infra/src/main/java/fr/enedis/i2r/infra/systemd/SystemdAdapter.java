package fr.enedis.i2r.infra.systemd;

import java.util.List;
import java.util.stream.Collectors;

import org.freedesktop.dbus.exceptions.DBusException;
import org.freedesktop.systemd1.Manager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.infra.hal.DbusConnectionManager;
import fr.enedis.i2r.system.ports.SystemdPort;

/**
 * La documentation de <b>Manager</b> de systemd est consultable
 * <a href="https://www.freedesktop.org/software/systemd/man/latest/org.freedesktop.systemd1.html">ici</a>
 */
public class SystemdAdapter implements SystemdPort {
    private static final Logger logger = LoggerFactory.getLogger(SystemdAdapter.class);

    private Manager manager;

    public SystemdAdapter() {
        try {
            this.manager = DbusConnectionManager.getInstance().getRemoteObject(
                "org.freedesktop.systemd1",
                "/org/freedesktop/systemd1",
                Manager.class);
            logger.debug("Systemd dBus manger on");
        } catch (DBusException e) {
            logger.error("Error while instantiating SystemdAdapter", e);
        }
    }

    /**
     * Active une liste d'unités systemd afin qu'elles soient démarrées automatiquement au boot.
     *
     * <p>Cette méthode appelle {@code EnableUnitFiles(...)} de l'interface {@code org.freedesktop.systemd1.Manager}
     * pour activer les services fournis. Elle passe la liste des noms d’unités, avec les options {@code runtime=false} et {@code force=true}.</p>
     *
     * <h3>Paramètres passés à {@code EnableUnitFiles} :</h3>
     * <ul>
     *   <li><b>files</b> – Liste des noms d’unités (ex: {@code my-service.service}) à activer.</li>
     *
     *   <li><b>runtime = false</b> – Indique que les liens symboliques d’activation seront écrits de manière permanente,
     *   c’est-à-dire dans le système de fichiers réel (et non temporairement dans /run).</li>
     *
     *   <li><b>force = true</b> – Force l'activation même si des conflits ou doublons existent (par exemple, si l’unité est déjà activée
     *   ou entre en conflit avec une autre). Cela écrasera les anciens liens symboliques si nécessaire.</li>
     * </ul>
     *
     * @param services liste des services à activer dans systemd.
     */
    @Override
    public void enableService(List<String> services) {
        manager.EnableUnitFiles(services, false, true);
        logger.debug("enableService: {}", services.stream().collect(Collectors.joining(", ")));
    }

    /**
     * Démarre une liste d'instances de service via l'interface D-Bus de systemd.
     *
     * <p>Pour chaque service de la liste, cette méthode appelle {@code StartUnit(serviceName, "replace")}
     * via l'interface {@code org.freedesktop.systemd1.Manager}, afin de lancer l'unité correspondante
     * dans systemd.</p>
     *
     * <p>La méthode {@code StartUnit} accepte un second paramètre, {@code mode}, qui indique à systemd
     * comment gérer les conflits éventuels entre unités lors de l'exécution de la commande. Cette
     * implémentation utilise le mode {@code "replace"}.</p>
     *
     * <h3>Valeurs possibles pour le paramètre {@code mode} (d'après la documentation officielle de systemd) :</h3>
     * <ul>
     *   <li><b>"replace"</b> – Démarre l'unité et ses dépendances en remplaçant, si nécessaire, les jobs déjà
     *   en attente qui seraient en conflit. (C'est le mode par défaut et celui utilisé ici.)</li>
     *
     *   <li><b>"fail"</b> – Démarre l'unité uniquement si cela n'entre pas en conflit avec d'autres jobs déjà
     *   planifiés. Sinon, la méthode échoue.</li>
     *
     *   <li><b>"isolate"</b> – Démarre uniquement l'unité cible et arrête toutes les autres unités qui ne
     *   sont pas des dépendances de celle-ci. Utilisé pour changer de niveau de fonctionnement (target).</li>
     *
     *   <li><b>"ignore-dependencies"</b> – Démarre l'unité sans tenir compte de ses dépendances. À utiliser avec précaution.</li>
     *
     *   <li><b>"ignore-requirements"</b> – Démarre l'unité même si certaines exigences (comme des dépendances requises)
     *   ne sont pas satisfaites. Ce mode est également risqué.</li>
     * </ul>
     *
     * @param services la liste des services à démarrer via systemd.
     */
    @Override
    public void startService(List<String> services) {
        for (String service : services) {
            manager.StartUnit(service, "replace");
            logger.debug("startService : {}", service);
        }
    }
}
