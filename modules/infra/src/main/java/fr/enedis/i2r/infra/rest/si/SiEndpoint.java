package fr.enedis.i2r.infra.rest.si;

import java.util.Map;

public enum SiEndpoint {
    SendConfig("/SIService/{idms}/db/cfg", "PUT");

    public final String endpointTemplate;
    public final String httpMethod;

    SiEndpoint(String endpointTemplate, String httpMethod) {
        this.endpointTemplate = endpointTemplate;
        this.httpMethod = httpMethod;
    }

    public String getEndpointPath(Map<String, String> pathParams) {
        String path = endpointTemplate;

        for (Map.Entry<String, String> entry : pathParams.entrySet()) {
            path = path.replace("{" + entry.getKey() + "}", entry.getValue());
        }

        return path;
    }
}
