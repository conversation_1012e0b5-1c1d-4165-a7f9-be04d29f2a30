package fr.enedis.i2r.infra.rest.config;

import java.util.ArrayList;
import java.util.List;

import fr.enedis.i2r.comsi.status.BipStatus;
import fr.enedis.i2r.infra.rest.si.ao2.Base;
import fr.enedis.i2r.infra.rest.si.ao2.DM;

public class StateChange extends Base {
    private List<Base> objects = new ArrayList<>();
    private int nb;

    public StateChange() {
        this.setName("cfg");
        this.setClazz("Container");
    }

    public List<Base> getObjects() {
        return this.objects;
    }

    public int getNb() {
        return nb;
    }

    public void addObject(Base object) {
        this.objects.add(object);
        nb = this.objects.size();
    }

    public static StateChange from(BipStatus newStatus) {
        var stateChange = new StateChange();

        var dm = new DM();
        dm.setState(newStatus.statusCode);

        stateChange.addObject(dm);

        return stateChange;
    }
}
