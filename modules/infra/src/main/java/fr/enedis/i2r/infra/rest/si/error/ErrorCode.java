package fr.enedis.i2r.infra.rest.si.error;

public enum ErrorCode {
    EMPTY_FIELD("EMPTY_FIELD"),
    INVALID_FIELD("INVALID_FIELD"),
    UNAUTHORIZED("UNAUTHORIZED"),
    BAD_CONTENT("BAD_CONTENT"),
    UNDISCOVERED("UNDISCOVERED"),
    UNSUPPORTED_VERSION("UNSUPPORTED_VERSION");

    private final String value;

    ErrorCode(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
