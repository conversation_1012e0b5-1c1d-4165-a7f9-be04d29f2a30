package fr.enedis.i2r.infra.rest.si.error;

import java.util.Map;

public class Message {
    private String body;
    private Map<String, Object> headers;

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public Map<String, Object> getHeaders() {
        return headers;
    }

    public void setHeaders(Map<String, Object> headers) {
        this.headers = headers;
    }

}
