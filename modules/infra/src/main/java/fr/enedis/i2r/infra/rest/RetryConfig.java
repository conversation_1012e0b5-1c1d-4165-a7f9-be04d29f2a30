package fr.enedis.i2r.infra.rest;

import java.time.Duration;

/**
 * Configuration pour un mécanisme de réessai (retry).
 * <p>
 * Cette classe est une {@code record} Java représentant les paramètres de
 * configuration
 * pour effectuer des tentatives répétées d'une opération après un échec.
 * </p>
 *
 * @param maximumAttempts Le nombre maximal de tentatives autorisées.
 * @param delay           La durée d'attente entre deux tentatives.
 */
public record RetryConfig(Integer maximumAttempts, Duration delay) {

    /**
     * Crée une configuration par défaut pour les tentatives de réexécution.
     * <p>
     * Par défaut, la configuration permet 2 tentatives de réessai avec un délai de
     * 60 secondes entre chacune.
     * </p>
     *
     * @return une instance de {@code RetryConfig} avec les paramètres par défaut.
     */
    public static RetryConfig defaultConfig() {
        return new RetryConfig(3, java.time.Duration.ofSeconds(60));
    }
}
