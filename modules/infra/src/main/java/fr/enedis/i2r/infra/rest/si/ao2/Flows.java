package fr.enedis.i2r.infra.rest.si.ao2;

public class Flows extends Base {
    private Flow alarm;
    private Flow cfg;
    private Flow command;
    private Flow csr;
    private Flow ntp;
    private Flow reachable;
    private Flow stat;
    private Flow upgrade;
    private Flow waitprog;
    private Flow init;

    public Flows() {
        this.setClazz("Container");
        this.setName("flows");
    }

    public Flow getAlarm() {
        return alarm;
    }

    public void setAlarm(Flow alarm) {
        this.alarm = alarm;
    }

    public Flow getCfg() {
        return cfg;
    }

    public void setCfg(Flow cfg) {
        this.cfg = cfg;
    }

    public Flow getCommand() {
        return command;
    }

    public void setCommand(Flow command) {
        this.command = command;
    }

    public Flow getCsr() {
        return csr;
    }

    public void setCsr(Flow csr) {
        this.csr = csr;
    }

    public Flow getNtp() {
        return ntp;
    }

    public void setNtp(Flow ntp) {
        this.ntp = ntp;
    }

    public Flow getReachable() {
        return reachable;
    }

    public void setReachable(Flow reachable) {
        this.reachable = reachable;
    }

    public Flow getStat() {
        return stat;
    }

    public void setStat(Flow stat) {
        this.stat = stat;
    }

    public Flow getUpgrade() {
        return upgrade;
    }

    public void setUpgrade(Flow upgrade) {
        this.upgrade = upgrade;
    }

    public Flow getWaitprog() {
        return waitprog;
    }

    public void setWaitprog(Flow waitprog) {
        this.waitprog = waitprog;
    }

    public Flow getInit() {
        return init;
    }

    public void setInit(Flow init) {
        this.init = init;
    }
}
