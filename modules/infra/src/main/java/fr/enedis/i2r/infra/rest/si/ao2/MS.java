package fr.enedis.i2r.infra.rest.si.ao2;

public class MS extends Base {
    private int count;
    private String cplc;
    private String secConfig;
    private String securityMod;

    public MS() {
        this.setClazz("MS");
        this.setName("ms");
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public String getCplc() {
        return cplc;
    }

    public void setCplc(String cplc) {
        this.cplc = cplc;
    }

    public String getSecConfig() {
        return secConfig;
    }

    public void setSecConfig(String secConfig) {
        this.secConfig = secConfig;
    }

    public String getSecurityMod() {
        return securityMod;
    }

    public void setSecurityMod(String securityMod) {
        this.securityMod = securityMod;
    }
}
