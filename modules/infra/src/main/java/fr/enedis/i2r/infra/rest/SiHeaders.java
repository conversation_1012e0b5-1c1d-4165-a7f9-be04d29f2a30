package fr.enedis.i2r.infra.rest;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class SiHeaders {
    public interface Constants {
        // Generic headers
        public final String ACCEPT = "Accept";
        public final String ACCEPT_ENCODING = "Accept-Encoding";
        public final String CONTENT_TYPE = "Content-Type";
        public final String CONTENT_ENCODING = "Content-Encoding";

        // Enedis headers
        public final String X_ERDF_HASH_HEADER = "x-erdf-hash";
        public final String X_ERDF_API_VERSION_HEADER = "x-erdf-api-version";
        public final String MA_CORRELATION_ID = "maCorrelationId";
        public final String MA_PROCESS_ID = "maProcessId";
        public final String MA_SOURCE = "maSource";
        public final String MA_DATE_EMISSION_FLUX = "maDateEmissionFlux";
        public final String MA_TYPE = "maType";
        public final String MA_VERSION = "maVersion";
        public final String MA_ID_PRM = "maIdPrm";
        public final String MA_ID_AFFAIRE = "maIdAffaire";
        public final String MA_ID_EXTERNE = "maIdExterne";
        public final String MA_ADS_BOITER = "maAdsBoitier";

        // Headers common values
        // TODO: These values are probably already defined somewhere in a library, use
        // those instead
        public final String JSON_TYPE = "application/json";
        public final String ZIP_FORMAT = "gzip";
    }

    public String ads;
    public String hash;

    public SiHeaders(String ads, String hash) {
        this.ads = ads;
        this.hash = hash;
    }

    public Map<String, String> toMap() {
        HashMap<String, String> headers = new HashMap<String, String>();

        headers.put(SiHeaders.Constants.CONTENT_TYPE, SiHeaders.Constants.JSON_TYPE);
        headers.put(SiHeaders.Constants.ACCEPT, SiHeaders.Constants.JSON_TYPE);
        headers.put(SiHeaders.Constants.CONTENT_ENCODING, SiHeaders.Constants.ZIP_FORMAT);
        headers.put(SiHeaders.Constants.ACCEPT_ENCODING, SiHeaders.Constants.ZIP_FORMAT);

        // TODO: Investigate what 2.0 is exactly
        headers.put(SiHeaders.Constants.X_ERDF_API_VERSION_HEADER, "2.0");
        // qqqq l'identifiant DU MESSAGE de corrélation au format UUID
        headers.put(SiHeaders.Constants.MA_CORRELATION_ID, UUID.randomUUID().toString());
        // pour l'identifiant de processus SI (le n° de la CTD) >> Champ présent mais
        // non exploité aujourd'hui (05/11/2020)
        headers.put(SiHeaders.Constants.MA_PROCESS_ID, "CT0017");
        // pour l’identifiant de l'application qui émet l’événement (NNA)
        headers.put(SiHeaders.Constants.MA_SOURCE, "IBIS");
        // (ex : iDT vers iCoeur) pour ordonner le traitement des flux. Date au format
        // timestamp epoch
        headers.put(SiHeaders.Constants.MA_DATE_EMISSION_FLUX, String.valueOf(Instant.now().toEpochMilli()));
        // pour identifier le nom fonctionnel de l'événement
        // (ex : teleoperationPriseEnCharge)
        headers.put(SiHeaders.Constants.MA_TYPE, "ConfigurationEvent");
        // pour le suivi des versions du flux (qui peut être incrémenté pour prise en
        // compte de nouveaux besoins) identifiants fonctionnels:
        headers.put(SiHeaders.Constants.MA_VERSION, "1.0");
        headers.put(SiHeaders.Constants.MA_ADS_BOITER, ads);

        headers.put(SiHeaders.Constants.X_ERDF_HASH_HEADER, hash);

        return headers;
    }
}
