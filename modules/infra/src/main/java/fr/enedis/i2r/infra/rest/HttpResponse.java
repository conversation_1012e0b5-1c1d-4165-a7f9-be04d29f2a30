package fr.enedis.i2r.infra.rest;

import io.javalin.http.HttpStatus;

public class HttpResponse {

    private HttpStatus status = HttpStatus.UNKNOWN;
    private String rawBody = "";

    public HttpStatus getStatus() {
        return status;
    }

    public void setStatus(HttpStatus status) {
        this.status = status;
    }

    public String getRawBody() {
        return rawBody;
    }

    public void setRawBody(String rawBody) {
        this.rawBody = rawBody;
    }

    public boolean isSuccess() {
        return this.getStatus().isSuccess();
    }

}
