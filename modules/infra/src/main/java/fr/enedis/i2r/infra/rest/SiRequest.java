package fr.enedis.i2r.infra.rest;

import static fr.enedis.i2r.infra.rest.Encoding.GZIP;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.Map;
import java.util.Optional;

import fr.enedis.i2r.comsi.IpAddress;
import fr.enedis.i2r.infra.rest.si.SiEndpoint;

public record SiRequest(
    SiEndpoint siEndpoint,
    Map<String, String> pathParams,
    SiHeaders headers,
    Optional<Object> body
) {
    private static final Integer SI_HTTP_PORT = 8443;

    public HttpRequest toHttpRequest(IpAddress datacenterAddress) throws URISyntaxException {
        var uri = buildUri(datacenterAddress);

        return new HttpRequest(
            siEndpoint.httpMethod,
            uri,
            headers.toMap(),
            body.orElse(""),
            GZIP
        );
    }

    private URI buildUri(IpAddress ipSi) throws URISyntaxException {
        var path = fillPathTemplate(siEndpoint.endpointTemplate, pathParams);
        var scheme = "https";

        return new URI(scheme, null, ipSi.ip(), SI_HTTP_PORT, path, null, null);
    }

    private static String fillPathTemplate(final String template, Map<String, String> params) {
        String finalPath = new String(template);


        for (var entry : params.entrySet()) {
            finalPath = finalPath.replace("{" + entry.getKey() + "}", entry.getValue());
        }

        return finalPath;
    }
}
