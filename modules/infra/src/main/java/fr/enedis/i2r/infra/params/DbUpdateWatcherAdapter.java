package fr.enedis.i2r.infra.params;

import static java.util.Collections.emptyList;

import java.io.IOException;
import java.nio.file.FileSystems;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardWatchEventKinds;
import java.nio.file.WatchEvent;
import java.nio.file.WatchKey;
import java.nio.file.WatchService;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.comsi.params.ConfigurationValue;
import fr.enedis.i2r.comsi.ports.DatabaseUpdateWatcherPort;

public class DbUpdateWatcherAdapter implements DatabaseUpdateWatcherPort {

    private static final Logger logger = LoggerFactory.getLogger(DbUpdateWatcherAdapter.class);
    private final WatchService watchService;
    private final String databasePath;
    private SqliteParamsProvider paramsProvider;

    public DbUpdateWatcherAdapter(SqliteParamsProvider paramsProvider, String databasePath) throws IOException {
        this.paramsProvider = paramsProvider;
        this.databasePath = databasePath;
        this.watchService = FileSystems.getDefault().newWatchService();
    }

    @Override
    public List<ConfigurationValue> waitForUpdates()  {
        try {
            logger.info("en attente d'une modification de la base de données");
            while (true) {
                this.waitForFileChange();
                logger.debug("base de données mise à jour");
                var rawParameters = this.paramsProvider.fetchAndResetUpdatedParameters();

                var configurationValues = mapParametersToConfigurationValues(rawParameters);

                if (!configurationValues.isEmpty()) {
                    return configurationValues;
                }
            }

        } catch (Exception e) {
            logger.error("Error while watching the params db file", e);
        }

        return emptyList();
    }

    public List<ConfigurationValue> mapParametersToConfigurationValues(HashMap<String, String> rawParams) {
        return rawParams.entrySet().stream()
            .map(entry -> ConfigurationValue.from(entry.getKey(), entry.getValue()))
            .flatMap(Optional::stream)
            .toList();
    }

    private void waitForFileChange() throws IOException, InterruptedException {
        logger.info("Starting to watch params db file: {}", this.databasePath);

        Path dbFile = Paths.get(this.databasePath);
        Path dbDir = dbFile.getParent();
        dbDir.register(watchService, StandardWatchEventKinds.ENTRY_MODIFY);

        while (true) {
            WatchKey key = watchService.take();
            for (WatchEvent<?> event : key.pollEvents()) {
                WatchEvent.Kind<?> kind = event.kind();
                Path changed = (Path) event.context();

                if (kind == StandardWatchEventKinds.ENTRY_MODIFY &&
                        changed.equals(dbFile.getFileName())) {
                    logger.info("Change detected on params db file");
                    key.reset();
                    return;
                }
            }
            key.reset();
        }
    }
}
