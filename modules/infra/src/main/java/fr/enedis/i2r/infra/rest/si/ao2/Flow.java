package fr.enedis.i2r.infra.rest.si.ao2;

import java.util.List;

public class Flow extends Base {
    private int freq1;
    private int freq2;
    private Integer lb;
    private Integer lbMax;
    private int retry1;
    private int retry2;
    private List<FlowSlot> slots;
    private List<String> url1;
    private List<String> url2;

    public int getFreq1() {
        return freq1;
    }

    public void setFreq1(int freq1) {
        this.freq1 = freq1;
    }

    public int getFreq2() {
        return freq2;
    }

    public void setFreq2(int freq2) {
        this.freq2 = freq2;
    }

    public Integer getLb() {
        return lb;
    }

    public void setLb(Integer lb) {
        this.lb = lb;
    }

    public Integer getLbMax() {
        return lbMax;
    }

    public void setLbMax(Integer lbMax) {
        this.lbMax = lbMax;
    }

    public int getRetry1() {
        return retry1;
    }

    public void setRetry1(int retry1) {
        this.retry1 = retry1;
    }

    public int getRetry2() {
        return retry2;
    }

    public void setRetry2(int retry2) {
        this.retry2 = retry2;
    }

    public List<FlowSlot> getSlots() {
        return slots;
    }

    public void setSlots(List<FlowSlot> slots) {
        this.slots = slots;
    }

    public List<String> getUrl1() {
        return url1;
    }

    public void setUrl1(List<String> url1) {
        this.url1 = url1;
    }

    public List<String> getUrl2() {
        return url2;
    }

    public void setUrl2(List<String> url2) {
        this.url2 = url2;
    }
}
