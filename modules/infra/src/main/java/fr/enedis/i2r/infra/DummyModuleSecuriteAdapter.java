package fr.enedis.i2r.infra;

import java.util.Optional;

import fr.enedis.i2r.comsi.ports.BoardManagerPort;
import fr.enedis.i2r.comsi.ports.ModuleSecuritePort;

public class DummyModuleSecuriteAdapter implements ModuleSecuritePort {
    private BoardManagerPort boardManagerPort;

    public DummyModuleSecuriteAdapter(BoardManagerPort boardManagerPort) {
        this.boardManagerPort = boardManagerPort;
    }

    @Override
    public String getIdms() throws Exception {
        var ads = boardManagerPort.getAds();

        return getIdmsFromAds(ads).orElse("IDMSDETEST");
    }

    Optional<String> getIdmsFromAds(String ads) {
        return IdentiteDisponible.fromAds(ads).map(identite -> identite.idms);
    }

}
