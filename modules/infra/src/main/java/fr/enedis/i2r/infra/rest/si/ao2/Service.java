package fr.enedis.i2r.infra.rest.si.ao2;

public class Service extends Base {
    private String status;
    private String started;
    private String stopped;
    private String request;
    private int result;
    private String error;
    private boolean autostart;
    private int autostop;
    private LogConfig log;

    /**
     * Each service can have a different class / name, so make it mandatory in the
     * constructor
     *
     * @param className the service class
     * @param name      the service name
     */
    public Service(String className, String name) {
        this.setClazz(className);
        this.setName(name);
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStarted() {
        return started;
    }

    public void setStarted(String started) {
        this.started = started;
    }

    public String getStopped() {
        return stopped;
    }

    public void setStopped(String stopped) {
        this.stopped = stopped;
    }

    public String getRequest() {
        return request;
    }

    public void setRequest(String request) {
        this.request = request;
    }

    public int getResult() {
        return result;
    }

    public void setResult(int result) {
        this.result = result;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    public boolean isAutostart() {
        return autostart;
    }

    public void setAutostart(boolean autostart) {
        this.autostart = autostart;
    }

    public int getAutostop() {
        return autostop;
    }

    public void setAutostop(int autostop) {
        this.autostop = autostop;
    }

    public LogConfig getLog() {
        return log;
    }

    public void setLog(LogConfig log) {
        this.log = log;
    }
}
