package fr.enedis.i2r.infra.rest;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.zip.GZIPOutputStream;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;

import fr.enedis.i2r.infra.rest.si.error.IComBadRequestException;
import io.javalin.http.HttpStatus;

public class HttpClient {

    private static final Logger logger = LoggerFactory.getLogger(HttpClient.class);
    private SSLContext sslContext;

    public HttpClient(SSLContext sslContext) {
        this.sslContext = sslContext;
    }

    public HttpResponse retryRequest(HttpRequest httpRequest) throws IComBadRequestException {
        return retryRequest(httpRequest, RetryConfig.defaultConfig());
    }

    public HttpResponse retryRequest(
            HttpRequest httpRequest,
            RetryConfig retryConfig) throws IComBadRequestException {
        Optional<IComBadRequestException> lastError = Optional.empty();

        for (int attempts = 1; attempts <= retryConfig.maximumAttempts(); attempts++) {
            try {
                logger.info("Attempt {}/{} for {}", attempts, retryConfig.maximumAttempts(), httpRequest.uri());

                var response = sendRequest(httpRequest);

                logger.info("Successfully executed request {}", httpRequest.uri());
                return response;
            } catch (IComBadRequestException ex) {
                logger.error("erreur lors de l'envoi de la requête", ex);
                lastError = Optional.of(ex);
            }

            try {
                TimeUnit.SECONDS.sleep(retryConfig.delay().getSeconds());
            } catch (InterruptedException e) {
                logger.warn("Interrupted while waiting for retry, last failed response was returned", e);
            }
        }

        logger.info("Failed request {}", httpRequest.uri());
        throw lastError.orElse(new IComBadRequestException(500, "échec de l'envoi de la requête"));
    }

    /**
     * Envoie la requête contenue dans le paramètre.
     *<p>
     * Nous n'utilisons pas ici l'API `java.net.http` à cause de la compression
     * GZIP.
     *<p>
     * Afin de compresser la requête, il faut passer par GzipOutputStream. Et si
     * nous voulons utiliser les API récentes du JDK, il faut forcément stocker le
     * contenu gzippé dans une variable, avant de l'envoyer. Le souci : c'est que
     * cela fait de l'utilisation mémoire inutile.
     * Utiliser directement HttpUrlConnection nous permet de relayer la sortie de
     * GzipOutputStream directement vers le flux HTTP, sans le stocker chez nous.
     *<p>
     * Les piped(input/output)stream ne sont pas envisageables :
     * https://stackoverflow.com/questions/5778658/how-do-i-convert-an-outputstream-to-an-inputstream
     * @throws IComBadRequestException
     */
    public HttpResponse sendRequest(HttpRequest httpRequest) throws IComBadRequestException {
        logger.debug("Calling REST endpoint: {}", httpRequest.uri());
        var httpResponse = new HttpResponse();

        try {
            logger.debug("URL to be reached: {}", httpRequest.uri());
            JsonMapper jsonMapper = JsonMapper.builder().configure(MapperFeature.SORT_PROPERTIES_ALPHABETICALLY, true)
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .serializationInclusion(Include.NON_NULL)
                .build();
            String jsonPayload = jsonMapper.writeValueAsString(httpRequest.payload());

            logger.debug("JSON Payload: {}", jsonPayload);

            HttpsURLConnection conn = prepareHttpsURLConnection(httpRequest, httpRequest.uri());
            sendPayload(conn, jsonPayload, httpRequest.encoding());
            int responseCode = conn.getResponseCode();

            var status = HttpStatus.forStatus(responseCode);

            if(status.isError()) {
                throw new IComBadRequestException(responseCode, parseRequestBody(conn.getInputStream()));
            }

            httpResponse.setStatus(HttpStatus.forStatus(responseCode));
            logger.info("Response Code: {}", responseCode);

            String rawResponse = parseRequestBody(conn.getInputStream());
            httpResponse.setRawBody(rawResponse);

            logger.info("Response {}", rawResponse);

            conn.disconnect();
            return httpResponse;
        } catch (IComBadRequestException e) {
            throw e;
        } catch (Exception e) {
            logger.error("erreur inattendue", e);
            throw new IComBadRequestException(500, e.getMessage());
        }

    }

    private String parseRequestBody(InputStream connectionStream) throws IOException {
        StringBuilder responsePayload = new StringBuilder();
        try (BufferedReader br = new BufferedReader(new InputStreamReader(connectionStream, StandardCharsets.UTF_8))) {
            String responseLine;
            while ((responseLine = br.readLine()) != null) {
                responsePayload.append(responseLine.trim());
            }
        }

        return responsePayload.toString();
    }

    private HttpsURLConnection prepareHttpsURLConnection(HttpRequest httpRequest, URI uri)
            throws URISyntaxException, IOException {
        HttpsURLConnection conn = (HttpsURLConnection) uri.toURL().openConnection();

        conn.setRequestMethod(httpRequest.httpMethod());
        // Use our SSL Context
        conn.setSSLSocketFactory(sslContext.getSocketFactory());

        // Set headers
        httpRequest.headers().forEach(conn::setRequestProperty);

        conn.setDoOutput(true);

        return conn;
    }

    private void sendPayload(HttpsURLConnection connection, String jsonPayload, Encoding encoding) throws IOException {
        switch (encoding) {
            case GZIP: {
                connection.setRequestProperty("Content-Encoding", SiHeaders.Constants.ZIP_FORMAT);
                connection.setRequestProperty("Accept-Encoding", SiHeaders.Constants.ZIP_FORMAT);
                try (OutputStream os = new GZIPOutputStream(connection.getOutputStream())) {
                    os.write(jsonPayload.getBytes());
                    os.flush();
                }
            }
            case NONE: {
                try (OutputStream os = connection.getOutputStream()) {
                    os.write(jsonPayload.getBytes(StandardCharsets.UTF_8));
                    os.flush();
                }
            }
        }
    }
}
