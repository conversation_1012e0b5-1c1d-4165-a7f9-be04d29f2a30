package fr.enedis.i2r.infra.rest.si.ao2;

import java.util.List;
import java.util.Map;

public class Box extends Base {
    private boolean enable;
    private Integer status;
    private List<String> IPsecEndPoint;
    private Map<String, Object> additionalProperties;

    public Box() {
        this.setClazz("Box");
        this.setName("box");
    }

    public boolean getEnable() {
        return enable;
    }

    public void setEnable(boolean enable) {
        this.enable = enable;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public List<String> getIPsecEndPoint() {
        return IPsecEndPoint;
    }

    public void setIPsecEndPoint(List<String> IPsecEndPoint) {
        this.IPsecEndPoint = IPsecEndPoint;
    }

    public Map<String, Object> getAdditionalProperties() {
        return additionalProperties;
    }

    public void setAdditionalProperties(Map<String, Object> additionalProperties) {
        this.additionalProperties = additionalProperties;
    }
}
