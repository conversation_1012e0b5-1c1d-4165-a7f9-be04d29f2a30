package fr.enedis.i2r.infra.params;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class SqliteParamsHasher {

    private static final Logger logger = LoggerFactory.getLogger(SqliteParamsHasher.class);

    private final Configuration configuration;

    public SqliteParamsHasher(Configuration configuration) {
        this.configuration = configuration;
    }

    public String generateTableHash() {
        Map<String, String> parameters = this.configuration.parameters();

        List<String> dataRows = new ArrayList<>();
        for (Map.Entry<String, String> entry : parameters.entrySet()) {
            String rowString = entry.getKey().trim() + "|" + (entry.getValue() == null ? "" : entry.getValue().trim());
            dataRows.add(rowString.toLowerCase());
        }

        Collections.sort(dataRows);
        StringBuilder finalData = new StringBuilder();
        for (String row : dataRows) {
            finalData.append(row).append("\n");
        }

        return computeMD5(finalData.toString());
    }

    private String computeMD5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(input.getBytes());
            byte[] digest = md.digest();

            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                hexString.append(String.format("%02x", b));
            }

            String hash = hexString.toString();
            logger.debug("Hash generated: " + hash);
            return hash;
        } catch (NoSuchAlgorithmException e) {
            logger.error("MD5 algorithm not found", e);
            return "";
        }
    }
}
