package fr.enedis.i2r.infra.shell;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.StringReader;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.system.ports.ShellExecutorPort;
import fr.enedis.i2r.system.shell.ShellCommand;

public class ShellExecutorAdapter implements ShellExecutorPort {

    private static final Logger logger = LoggerFactory.getLogger(ShellExecutorAdapter.class);

    public BufferedReader execute(ShellCommand command) {
        try {
            Process process = Runtime.getRuntime().exec(command.getCommand());
            return new BufferedReader(new InputStreamReader(process.getInputStream()));

        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new BufferedReader(new StringReader(""));
        }
    }
}
