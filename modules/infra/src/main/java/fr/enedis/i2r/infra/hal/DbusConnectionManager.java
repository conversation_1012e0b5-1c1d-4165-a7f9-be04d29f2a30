package fr.enedis.i2r.infra.hal;

import org.freedesktop.dbus.connections.impl.DBusConnection;
import org.freedesktop.dbus.connections.impl.DBusConnectionBuilder;
import org.freedesktop.dbus.exceptions.DBusException;

public class DbusConnectionManager {

    private static DBusConnection instance;

    private DbusConnectionManager() {
    }

    public static synchronized DBusConnection getInstance() throws DBusException {
        if (instance == null) {
            instance = DBusConnectionBuilder.forSystemBus().build();
        }
        return instance;
    }
}
