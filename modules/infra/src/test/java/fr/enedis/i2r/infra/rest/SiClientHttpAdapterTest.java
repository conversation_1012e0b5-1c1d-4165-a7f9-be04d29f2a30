package fr.enedis.i2r.infra.rest;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.time.Instant;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;

import fr.enedis.i2r.comsi.ConfigurationBoitier;
import fr.enedis.i2r.comsi.errors.RequestToSiException;
import fr.enedis.i2r.comsi.ports.BoardManagerPort;
import fr.enedis.i2r.comsi.ports.ComsiParametersPort;
import fr.enedis.i2r.infra.rest.si.ao2.ConfigurationBoitierIcare;
import fr.enedis.i2r.infra.rest.si.error.IComBadRequestException;
import io.javalin.http.HttpStatus;

public class SiClientHttpAdapterTest {
    HttpClient httpClient;
    BoardManagerPort boardManagerPort;
    ComsiParametersPort parametersPort;

    @BeforeEach
    void setup() {
        httpClient = mock(HttpClient.class);
        boardManagerPort = mock(BoardManagerPort.class);
        parametersPort = mock(ComsiParametersPort.class);
    }

    @Test
    void le_client_envoie_la_configuration_boitier_au_datacenter_primaire() throws RequestToSiException, IComBadRequestException {
        CustomComSiConfiguration conf = new CustomComSiConfiguration();

        var successfulResponse = new HttpResponse();
        successfulResponse.setStatus(HttpStatus.OK);

        ArgumentCaptor<HttpRequest> captor = ArgumentCaptor.forClass(HttpRequest.class);

        when(httpClient.retryRequest(captor.capture())).thenReturn(successfulResponse);

        SiClientHttpAdapter siClient = new SiClientHttpAdapter(conf.build(), httpClient, parametersPort, "ADS", "IDMS");
        siClient.sendConfigurationBoitier(
                ConfigurationBoitier.from(conf.build(), Instant.now(), "HASH", "ADS", "IDMS", "ICCID"));

        HttpRequest requeteEnvoyee = captor.getValue();
        assertThat(requeteEnvoyee.uri().getHost()).isEqualTo(conf.ipPacy.ip());
    }

    @Test
    void la_configuration_est_envoyee_au_datacenter_secondaire_lorsque_l_envoi_echoue_sur_le_primaire() throws RequestToSiException, IComBadRequestException {
        CustomComSiConfiguration conf = new CustomComSiConfiguration();

        var notFoundResponse = new HttpResponse();
        notFoundResponse.setStatus(HttpStatus.BAD_REQUEST);

        ArgumentCaptor<HttpRequest> captor = ArgumentCaptor.forClass(HttpRequest.class);

        when(httpClient.retryRequest(captor.capture())).thenThrow(new IComBadRequestException(500, "erreur"));

        SiClientHttpAdapter siClient = new SiClientHttpAdapter(conf.build(), httpClient, parametersPort, "ADS", "IDMS");

        assertThatExceptionOfType(RequestToSiException.class).isThrownBy(() ->
            siClient.sendConfigurationBoitier(ConfigurationBoitier.from(conf.build(), Instant.now(), "HASH", "ADS", "IDMS", "ICCID"))
        );

        List<HttpRequest> requetesEnvoyees = captor.getAllValues();
        assertThat(requetesEnvoyees).hasSize(2);
        assertThat(requetesEnvoyees.get(0).uri().getHost()).isEqualTo(conf.ipPacy.toString());
        assertThat(requetesEnvoyees.get(1).uri().getHost()).isEqualTo(conf.ipNoe.toString());
    }

    @Test
    void la_configuration_boitier_est_correctement_serialisee_pour_icare() throws RequestToSiException, IComBadRequestException {
        var conf = new CustomComSiConfiguration().build();

        var now = Instant.now();
        var confBoitier = ConfigurationBoitier.from(conf, now, "HASH", "ADS", "IDMS", "ICCID");

        var successfulResponse = new HttpResponse();
        successfulResponse.setStatus(HttpStatus.OK);

        ArgumentCaptor<HttpRequest> captor = ArgumentCaptor.forClass(HttpRequest.class);

        when(httpClient.retryRequest(captor.capture())).thenReturn(successfulResponse);

        SiClientHttpAdapter siClient = new SiClientHttpAdapter(conf, httpClient, parametersPort, "ADS", "IDMS");
        siClient.sendConfigurationBoitier(confBoitier);

        HttpRequest requeteEnvoyee = captor.getValue();
        assertThat(requeteEnvoyee.uri().getHost()).isEqualTo(conf.parseDatacenterConfiguration().primaryIp().ip());
        assertThat(requeteEnvoyee.payload()).isInstanceOf(ConfigurationBoitierIcare.class);

        ConfigurationBoitierIcare sentConfiguration = (ConfigurationBoitierIcare) requeteEnvoyee.payload();
        assertThat(sentConfiguration.getNb()).isEqualTo(3);
        // TODO: tester plus de champs
    }

}
