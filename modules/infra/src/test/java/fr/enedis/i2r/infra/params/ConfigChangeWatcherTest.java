package fr.enedis.i2r.infra.params;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.sql.DriverManager;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;

import fr.enedis.i2r.comsi.ConfigurationChangeWatcher;
import fr.enedis.i2r.comsi.params.BipParameter;
import fr.enedis.i2r.infra.rest.CustomComSiConfiguration;
import fr.enedis.i2r.infra.rest.HttpClient;
import fr.enedis.i2r.infra.rest.HttpRequest;
import fr.enedis.i2r.infra.rest.HttpResponse;
import fr.enedis.i2r.infra.rest.SiClientHttpAdapter;
import fr.enedis.i2r.infra.rest.config.StateChange;
import fr.enedis.i2r.infra.rest.si.ao2.DM;
import fr.enedis.i2r.system.watchdog.ThreadWatchdog;
import io.javalin.http.HttpStatus;

public class ConfigChangeWatcherTest {
    private Path tempDir;
    private Path watchedFile;

    private DbUpdateWatcherAdapter dbUpdateWatcher;
    private ExecutorService executor;
    private SqliteParamsProvider paramsProvider;
    private SiClientHttpAdapter siClientPort;

    private HttpClient httpClient;

    @BeforeEach
    void setup() throws Exception{
        tempDir = Files.createTempDirectory("watch_test");
        watchedFile = tempDir.resolve("params.db");
        Files.createFile(watchedFile);

        executor = Executors.newSingleThreadExecutor();

        paramsProvider = new SqliteParamsProvider(watchedFile.toString());

        dbUpdateWatcher = new DbUpdateWatcherAdapter(paramsProvider, watchedFile.toString());

        httpClient = mock(HttpClient.class);

        siClientPort = new SiClientHttpAdapter(new CustomComSiConfiguration().build(), httpClient, paramsProvider, "ADS", "IDMS");
    }

    @AfterEach
    void teardown() throws IOException {
        executor.shutdownNow();

        Files.deleteIfExists(watchedFile);
        Files.deleteIfExists(tempDir);
    }

    @Test
    void le_si_est_informe_lors_du_changement_du_state() throws Exception {
        var configurationChangeWatcher = new ConfigurationChangeWatcher(dbUpdateWatcher, siClientPort.getSiConfigurationNotifier(), mock(ThreadWatchdog.class));
        executor.submit(configurationChangeWatcher);

        Thread.sleep(1000);

        var successfulResponse = new HttpResponse();
        successfulResponse.setStatus(HttpStatus.OK);

        ArgumentCaptor<HttpRequest> captor = ArgumentCaptor.forClass(HttpRequest.class);

        var connection = DriverManager.getConnection("jdbc:sqlite:" + watchedFile.toString());
        var sql = "INSERT INTO parameters(param_name, param_value) VALUES(?, ?)";
        var upsertStatement = connection.prepareStatement(sql);

        upsertStatement.setString(1, BipParameter.BipState.parameterKey);
        upsertStatement.setString(2, "2");

        upsertStatement.executeUpdate();

        Thread.sleep(1000);

        verify(httpClient).retryRequest(captor.capture());

        HttpRequest requeteEnvoyee = captor.getValue();
        assertThat(requeteEnvoyee.payload()).isInstanceOf(StateChange.class);

        StateChange request = (StateChange) requeteEnvoyee.payload();
        assertThat(request.getObjects().get(0)).isInstanceOf(DM.class);

        DM dmInRequest = (DM) request.getObjects().get(0);
        assertThat(dmInRequest.getState()).isEqualTo(2);
    }

    @Test
    void le_watcher_prend_en_compte_plusieurs_changements_d_affilee() throws Exception {
        var configurationChangeWatcher = new ConfigurationChangeWatcher(dbUpdateWatcher, siClientPort.getSiConfigurationNotifier(), mock(ThreadWatchdog.class));
        executor.submit(configurationChangeWatcher);

        Thread.sleep(1000);

        var successfulResponse = new HttpResponse();
        successfulResponse.setStatus(HttpStatus.OK);

        ArgumentCaptor<HttpRequest> captor = ArgumentCaptor.forClass(HttpRequest.class);

        var connection = DriverManager.getConnection("jdbc:sqlite:" + watchedFile.toString());
        var sql = "INSERT INTO parameters(param_name, param_value) VALUES(?, ?)";
        var upsertStatement = connection.prepareStatement(sql);

        upsertStatement.setString(1, BipParameter.BipState.parameterKey);
        upsertStatement.setString(2, "2");

        upsertStatement.executeUpdate();

        Thread.sleep(500);

        sql = "UPDATE parameters SET param_value = ? WHERE param_name = ?";
        upsertStatement = connection.prepareStatement(sql);

        upsertStatement.setString(1, "1");
        upsertStatement.setString(2, BipParameter.BipState.parameterKey);

        upsertStatement.executeUpdate();

        Thread.sleep(1000);

        verify(httpClient, times(2)).retryRequest(captor.capture());
        var requetes = captor.getAllValues();

        assertThat(requetes).hasSize(2);

        var dmInRequest = (DM) ((StateChange) requetes.get(0).payload()).getObjects().get(0);
        assertThat(dmInRequest.getState()).isEqualTo(2);

        dmInRequest = (DM) ((StateChange) requetes.get(1).payload()).getObjects().get(0);
        assertThat(dmInRequest.getState()).isEqualTo(1);
    }
}
