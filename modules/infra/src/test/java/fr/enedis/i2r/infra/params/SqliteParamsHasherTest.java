package fr.enedis.i2r.infra.params;

import static org.assertj.core.api.Assertions.assertThat;

import java.sql.SQLException;
import java.util.HashMap;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class SqliteParamsHasherTest {

    private SqliteParamsHasher hasher;

    @BeforeEach
    void setup() throws SQLException {
        var parametersMap = new HashMap<String, String>();
        parametersMap.put("clé1", "valeur1");
        parametersMap.put("clé2", "valeur2");
        Configuration configuration = new Configuration(parametersMap);

        hasher = new SqliteParamsHasher(configuration);
    }

    @Test
    void hash_change_lorsque_les_valeurs_sont_differentes() throws SQLException {
        String hash1 = hasher.generateTableHash();

        var parametersMap = new HashMap<String, String>();
        parametersMap.put("clé1", "autre_valeur");
        Configuration configuration = new Configuration(parametersMap);

        hasher = new SqliteParamsHasher(configuration);

        String hash2 = hasher.generateTableHash();

        assertThat(hash1).isNotEqualTo(hash2);
    }

    @Test
    void hash_est_identique_pour_des_valeurs_avec_casses_differentes() throws SQLException {
        String hash1 = hasher.generateTableHash();

        var parametersMap = new HashMap<String, String>();
        parametersMap.put("clé1", "valEUR1");
        parametersMap.put("CLé2", "valeur2");
        Configuration configuration = new Configuration(parametersMap);

        hasher = new SqliteParamsHasher(configuration);

        String hash2 = hasher.generateTableHash();

        assertThat(hash1).isEqualTo(hash2);
    }

    @Test
    void hash_est_identique_pour_des_valeurs_identiques_mais_dans_un_ordre_different() throws SQLException {
        String hash1 = hasher.generateTableHash();

        var parametersMap = new HashMap<String, String>();
        parametersMap.put("clé2", "valeur2");
        parametersMap.put("clé1", "valeur1");
        Configuration configuration = new Configuration(parametersMap);

        hasher = new SqliteParamsHasher(configuration);

        String hash2 = hasher.generateTableHash();

        assertThat(hash1).isEqualTo(hash2);
    }

    @Test
    void hash_est_identique_pour_des_valeurs_avec_espaces_supplementaires() throws SQLException {
        String hash1 = hasher.generateTableHash();

        var parametersMap = new HashMap<String, String>();
        parametersMap.put("clé1", "  valeur1  ");
        parametersMap.put("clé2", "valeur2  ");
        Configuration configuration = new Configuration(parametersMap);

        hasher = new SqliteParamsHasher(configuration);

        String hash2 = hasher.generateTableHash();

        assertThat(hash1).isEqualTo(hash2);
    }
}
