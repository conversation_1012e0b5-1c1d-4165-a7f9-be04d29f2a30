FILES_PATH="/var/lib/i2r/"

# création ce l'autorité de certification
keytool -genkeypair -alias localRootCA -keyalg RSA -keysize 2048 -validity 3650 \
    -keystore "$FILES_PATH"local-dev-ca.jks -storepass password -keypass password \
    -dname "cn=local dev CA, ou=dev, o=i2R" -ext bc:cacerts
## export du certificat de l'AC
keytool -exportcert -alias localRootCA -keystore "$FILES_PATH"local-dev-ca.jks \
      -storepass password -rfc -file /tmp/localRootCA.crt

# partie serveur :
## certificat
keytool -genkeypair -alias si-mock -keyalg RSA -keysize 2048 -validity 3650 \
    -keystore "$FILES_PATH"si-mock-certificate.jks -storepass password -keypass password \
    -dname "cn=si-mock, ou=dev, o=i2R"
## création du Certificate Signing Request (CSR)
keytool -certreq -alias si-mock \
    -keystore "$FILES_PATH"si-mock-certificate.jks -storepass password \
    -file "$FILES_PATH"si-mock.csr
## signature du certificat
keytool -gencert -alias localRootCA -keystore "$FILES_PATH"local-dev-ca.jks -storepass password \
    -infile "$FILES_PATH"si-mock.csr -outfile "$FILES_PATH"si-mock.cer -validity 3650 \
    -ext san=ip:127.0.0.1
## copie du certificat du CA dans le jks du si-mock
keytool -importcert -alias localRootCA -file /tmp/localRootCA.crt \
      -keystore "$FILES_PATH"si-mock-certificate.jks -storepass password -noprompt
## ajout du certificat signé du si-mock au jks du si-mock
keytool -importcert -alias si-mock -file "$FILES_PATH"si-mock.cer \
    -keystore "$FILES_PATH"si-mock-certificate.jks -storepass password -noprompt
## création du truststore pour le si-mock
keytool -importcert -alias localRootCA -file /tmp/localRootCA.crt \
      -keystore "$FILES_PATH"si-mock-truststore.jks -storepass password -noprompt

# partie client :
## certificat
keytool -genkeypair -alias bip-local -keyalg RSA -keysize 2048 -validity 3650 \
    -keystore "$FILES_PATH"certificate.jks -storepass password -keypass password \
    -dname "cn=bip-local, ou=dev, o=i2R"
## création du Certificate Signing Request (CSR)
keytool -certreq -alias bip-local \
    -keystore "$FILES_PATH"certificate.jks -storepass password \
    -file "$FILES_PATH"bip-local.csr
## signature du certificat
keytool -gencert -alias localRootCA -keystore "$FILES_PATH"local-dev-ca.jks -storepass password \
    -infile "$FILES_PATH"bip-local.csr -outfile "$FILES_PATH"bip-local.cer -validity 3650 \
    -ext san=ip:127.0.0.1
## copie du certificat du CA dans le jks du bip
keytool -importcert -alias localRootCA -file /tmp/localRootCA.crt \
      -keystore "$FILES_PATH"certificate.jks -storepass password -noprompt
## ajout du certificat signé du si-mock au jks du si-mock
keytool -importcert -alias bip-local -file "$FILES_PATH"bip-local.cer \
    -keystore "$FILES_PATH"certificate.jks -storepass password -noprompt
## création du truststore pour le bip
keytool -importcert -alias localRootCA -file /tmp/localRootCA.crt \
      -keystore "$FILES_PATH"truststore.jks -storepass password -noprompt

## nettoyage
rm /tmp/localRootCA.crt
rm "$FILES_PATH"*.cer
rm "$FILES_PATH"*.csr
